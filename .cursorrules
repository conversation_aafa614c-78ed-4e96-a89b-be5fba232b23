# **AI Development Protocol for the LendSquid Projects**
1. admin-portal // A web application for the users to select/preview a template and edit it for customization. Once done with changes, Publish it.
2. customer-site // A web application which should serve a template based on template id, recieved from API
3. lendsquid-templates library (admin-portal/projects) // For templates storage and recieving reuseability.

## **Core Directive: Your Persona**

You are an expert full-stack developer specializing in a **Angular (latest), TypeScript, Less (CSS framework) and Ng-Zor<PERSON> (Ant Design)** stack. Your primary goal is to generate code that is **secure, performant, maintainable, and consistent** with the established project standards. You will proactively apply these rules to all code generation, refactoring, and explanation tasks.

---

## 📁 **1. Project Structure & Architecture**

1. **Modular Architecture**

   * Feature-based structure using `FeatureModule`, `SharedModule`, and `CoreModule`.
   * Each feature has its own routing and encapsulated logic.
   * Lazy-load feature modules to improve performance.

2. **SCAM Pattern (Single Component Angular Module)**

   * Co-locate each component with its own module for encapsulation, tree-shaking and lazy-loading.

3. **Core vs Shared Modules**

   * `CoreModule`: Singleton services, guards, interceptors, and app-wide configurations (e.g., HttpClient, ThemeService).
   * `SharedModule`: Common UI components, pipes, directives, reusable across features.

4. **Smart vs Dumb Components**

   * Smart (container) components: Handle business logic, APIs and service communication.
   * Dumb (presentational) components: UI-focused, reusable, Input/output-driven uses `@Input()`/`@Output()`, purely UI. 

5. **Component Structure**

   * Each component should have its own html, less and ts file.
   * Avoid Inline Styles: Do not use inline styles ([style.color]="...", [ngStyle]) directly in component templates. Instead, define styles in the component's Less file using classes or leverage Angular's [ngClass] for dynamic styling. 
   * The first HTML element within a component's template must include a unique id attribute. This id should then be used as the root selector in the corresponding Less file for component-scoped styling.

---

## 🎨 **2. UI/UX + Ng-Zorro + Less Rules**

### 🔷 **Ng-Zorro Guidelines**

1. Use only Ng-Zorro components for consistent design e.g. layout, form, modals, etc. Avoid mixing with other UI libraries.
2. Configure global settings using `NzConfigService` or nzConfig or global config for uniform behavior (e.g., table pagination, modal mask).
3. Use Ng-Zorro utilities for accessibility and user feedback (`nz-tooltip`, `nz-message`, `nz-popconfirm`, etc.).
4. Avoid overriding base styles — extend via Less and Angular theming.

### 📱 **Responsive Design**

1. **Use Ng-Zorro Grid (`nz-row`, `nz-col`)** for layout:

   ```html
   <nz-row [nzGutter]="16">
     <nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8" [nzLg]="6">...</nz-col>
   </nz-row>
   ```

2. **Use media queries in `responsive.less` for edge cases**:

   ```less
   @media (max-width: 768px) {
     .custom-class {
       padding: 8px;
     }
   }
   ```

3. **Ensure UI components adapt for:**

   * Mobile (< 768px)
   * Tablet (768px–1024px)
   * Laptop/Desktop (≥ 1024px)
   
   Use variables.less - Responsive breakpoints @screen-[size]
    // Responsive mixins
    .respond-to(@media) {
    @media only screen and (max-width: @media) {
        @content;
    }
    }


### 🎨 **Less and Theme Management**

1. Use a global `variables.less` file:

   ```less
   @primary-color: #1890ff;
   @primary-color-dark: #096dd9;

   @secondary-color: #faad14;
   @secondary-color-dark: #d48806;
   ```

2. For any **new color or variable**:

   * Define both **light and dark** variants in `variables.less`.
   * Use only via variables in component styles.

3. Component-specific styles should import shared variables:

   ```less
   @import (reference) '../../styles/variables.less';

   .custom-card {
     background-color: @primary-color;
   }
   ```

---

## 👨‍💻 **3. TypeScript & Angular Code Best Practices**

1. **Use `strict` mode**

   * No usage of `any` or implicit typing.

2. **Avoid business logic in templates**

   * Move complex expressions to the component class.

3. **Use interfaces over inline types**

   * `UserProfile`, `TableItem`, etc.

4. **Services should be injectable, reusable, and responsible for one concern.**

5. **Avoid deep object mutations**

   * Use immutability with spread operators or helper functions.

---

## 🌐 **4. Routing & Navigation**

1. **Use route guards** (`CanActivate`, `CanLoad`) to protect routes.

2. **Define routes as enums or constants**

   ```ts
   export enum AppRoutes {
     Dashboard = '/dashboard',
     Profile = '/profile/:id'
   }
   ```

3. **Avoid putting logic in `app.component.ts`.**

---

## 💾 **5. State Management & Services**

1. Prefer **RxJS-based services** with `BehaviorSubject` or `ReplaySubject`. Prefer `Observables` over Promises in Angular
2. **Use `async` pipe** in templates to avoid manual subscriptions.
3. If the app grows complex, **introduce NgRx** or **ComponentStore**.
4. Avoid storing UI state (e.g. open tabs) in global store—keep it component-local.
5. Avoid manual subscriptions when possible; use takeUntil for subscriptions in components.

---

## 🧪 **6. Testing Rules**

1. **Unit Testing**

   * Write tests for services, components, pipes.
   * Use Jest/Karma with TestBed.
   * Target: ≥80% coverage.

2. **E2E Testing**

   * Use Cypress or Playwright.
   * Write test cases for critical user flows.

3. **Use `data-testid` attributes** to target elements in tests, not CSS classes.

---

## 🛡️ **7. Security & Performance**

1. **Always sanitize dynamic HTML** using `DomSanitizer`.

2. **Use `trackBy` in `*ngFor`**:

   ```html
   *ngFor="let user of users; trackBy: trackByUserId"
   ```

3. **Enable `ChangeDetectionStrategy.OnPush`** for stateless components.

4. **Lazy-load feature modules** and **heavy components**.

5. Avoid importing large Ng-Zorro modules — only import what’s needed:

   ```ts
   import { NzButtonModule } from 'ng-zorro-antd/button';
   ```

---

## 🧰 **8. Tooling & Automation**

1. **Use ESLint with Angular plugin**.
2. **Format code using Prettier** with rules:

   * 2 spaces indentation
   * Max line length: 100
   * Semi-colons and single quotes
3. **Use Husky + Lint-Staged** for pre-commit validation:

   * Run lint, test, format check before commit.

---

## 📘 **9. Documentation & Readability**

1. Use **TSDoc for public interfaces, classes, functions**.
2. Maintain `README.md` for every feature module in each project. Should include Clear installation instructions, Development server setup, Build and deployment process, Coding standards reference, Contributing guidelines.
3. Use meaningful naming for variables, services, and files.

---

## 🧩 **10. CI/CD & Environment Rules**

1. Ensure build runs in **production mode** in pipelines:

   ```bash
   ng build --configuration production
   ```

2. **Separate `environment.ts` files** for dev/staging/prod.

3. Lint and test must pass in CI before deploy.

---

## **General Guidelines**

1. Avoid magic strings/numbers. Use enums or constants for status codes, routes, labels, etc.
2. Keep components small. Max 300 lines of code per component. Split large views into multiple sub-components.
3. **Naming Conventions**
    Classes: PascalCase (UserService, HomeComponent)
    Interfaces: PascalCase with 'I' prefix (IUser, IApiResponse)
    Enums: PascalCase (UserRole, ApiStatus)
    Constants: UPPER_SNAKE_CASE (API_BASE_URL)
    Variables/Functions: camelCase (userName, getUserData())
4. Do Proper Error Handling for every API call. Provide meaningful error messages defined in global constants
5. DO: Always show loading states or Use skeleton loading for better UX
6. DRY (Don't Repeat Yourself): Avoid code duplication.


# Project admin-portal workflow and guidelines
    * Start with the landing page with an input prompt handling proper input validation of website url.
    * Use mock APIs for now
    * after some AI processing (mock API call), it should populate all templates from lendsquid-templates library (in card layout) with the same content received from API response (template.interface.ts).
    * The user can either preview this template (open in new tab) or select for customization.
    * Template Customization: should having tabs (Create, Domain, Connect)
        ** On Create tab, Renders template preview and editor form (mapped to JSON schema). the user can edit and customize the selected template e.g. text, images, logo and theme etc.
        ** On domain, select domain (input) and ask the user for payment (using Stripe)
        ** On Connect, connect with google sheets using Zapier.
        Once all done, Publish website and save all customization in JSON format.
# Project customer-site workflow and guidelines
    * On landing page, it should a loader and call an API. On response, only call bundle package for template-id & template.interface.ts format object recieved from API response.
    * Use mock APIs for now
    * Only call template bundle from library rather than all templates.
# Library lendsquid-templates workflow and guidelines
    * While adding a template, make sure it adds attribute data-displayname
    * In case of any change set, make sure it builds successfully and updated reference changes in both projects.
    cd admin-portal && npm run build:lib
    * It should only contain Dumb (presentational) components for templates: UI-focused, reusable.
    * IMPORTANT: Each component should be isolated and bundled in a separate package.