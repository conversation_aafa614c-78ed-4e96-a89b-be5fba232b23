export interface Template {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  theme: ThemeConfiguration;
  sections: TemplateSection[];
  metadata: TemplateMetadata;
}

export interface ThemeConfiguration {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  secondaryBackgroundColor: string;
  primaryTextColor: string;
  secondaryTextColor: string;
  tertiaryTextColor: string;
  fontFamily: string;
}

export interface TemplateSection {
  type: 'header' | 'title' | 'calculators' | 'footer';
  title: string;
  content: HeaderContent | TitleContent | CalculatorContent | FooterContent;
}

export interface HeaderContent {
  logo: string;
  logoStyle: 'image' | 'text';
  logoAlt: string;
  navigation: Array<{
    label: string;
    url: string;
  }>;
  ctaButton: {
    text: string;
    url: string;
    style: string;
  };
}

export interface TitleContent {
  mainTitle: string;
  subtitle: string;
  description?: string;
  heroImage: string;
  heroImageAlt: string;
  ctaButtons: Array<{
    text: string;
    url: string;
    style: string;
    size: string;
  }>;
}

export interface CalculatorContent {
  title: string;
  description: string;
  calculators: Array<{
    id: string;
    name: string;
    type: string;
    description: string;
    isEnabled: boolean;
  }>;
}

export interface FooterContent {
  companyInfo?: {
    name: string;
    logo: string;
    description: string;
    tagline?: string;
  };
  contactInfo?: {
    address: string;
    phone: string;
    email: string;
    website: string;
  };
  socialMedia?: Array<{
    platform: string;
    url: string;
    icon: string;
  }>;
  links?: Array<{
    title: string;
    links: Array<{
      label: string;
      url: string;
    }>;
  }>;
  newsletter?: {
    title: string;
    placeholder: string;
    buttonText: string;
  };
  copyright: string;
  privacyPolicy?: {
    label: string;
    url: string;
  };
  profileSection?: {
    title: string;
    subtitle: string;
    description: string;
    profileImage: string;
    profileName: string;
    profileTitle: string;
    credentials: string[];
    specialties: string[];
  };
}

export interface TemplateMetadata {
  createdAt: string;
  updatedAt: string;
  version: string;
  author: string;
} 