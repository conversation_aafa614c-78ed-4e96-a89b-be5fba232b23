import { Injectable } from '@angular/core';

export interface TemplateModule {
  [key: string]: any;
}

@Injectable({
  providedIn: 'root'
})
export class TemplateLoaderService {
  private templateCache = new Map<string, any>();

  /**
   * Dynamically loads a template component based on template ID
   * @param templateId - The unique identifier for the template
   * @returns Promise that resolves to the template component class
   */
  async loadTemplate(templateId: string): Promise<any> {
    // Check cache first
    if (this.templateCache.has(templateId)) {
      return this.templateCache.get(templateId);
    }

    try {
      const componentClass = await this.dynamicImport(templateId);
      this.templateCache.set(templateId, componentClass);
      return componentClass;
    } catch (error) {
      console.error(`Failed to load template ${templateId}:`, error);
      throw new Error(`Template ${templateId} not found or failed to load`);
    }
  }

  /**
   * Clears the template cache
   */
  clearCache(): void {
    this.templateCache.clear();
  }

  /**
   * Removes a specific template from cache
   * @param templateId - The template ID to remove from cache
   */
  removeFromCache(templateId: string): void {
    this.templateCache.delete(templateId);
  }

  /**
   * Dynamic import for template components
   * @param templateId - The template ID to import
   * @returns Promise that resolves to the component class
   */
  private async dynamicImport(templateId: string): Promise<any> {
    switch (templateId) {
      case 'lorem-ipsum':
        const loremModule = await import('../templates/lorem-ipsum/lorem-ipsum.component');
        return loremModule.LoremIpsumComponent;
      case 'ammarah':
        const ammarahModule = await import('../templates/ammarah/ammarah.component');
        return ammarahModule.AmmarahComponent;
      case 'template-1':
        const template1Module = await import('../templates/template-1/template-1.component');
        return template1Module.Template1Component;
      case 'template-2':
        const template2Module = await import('../templates/template-2/template-2.component');
        return template2Module.Template2Component;
      default:
        throw new Error(`Template ${templateId} not found`);
    }
  }
} 