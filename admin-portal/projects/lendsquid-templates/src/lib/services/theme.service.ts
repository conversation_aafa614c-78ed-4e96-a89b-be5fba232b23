import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { ThemeConfiguration } from '../models/template.interface';

export type ThemeMode = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private renderer: Renderer2;
  private currentThemeSubject = new BehaviorSubject<ThemeMode>('light');
  private currentThemeConfigSubject = new BehaviorSubject<ThemeConfiguration | null>(null);

  public currentTheme$ = this.currentThemeSubject.asObservable();
  public currentThemeConfig$ = this.currentThemeConfigSubject.asObservable();

  constructor(rendererFactory: RendererFactory2) {
    this.renderer = rendererFactory.createRenderer(null, null);
  }

  setThemeMode(mode: ThemeMode): void {
    this.currentThemeSubject.next(mode);
    this.applyThemeToDocument(mode);
  }

  setThemeConfiguration(config: ThemeConfiguration): void {
    this.currentThemeConfigSubject.next(config);
    // Set all theme variables as CSS variables
    const root = document.documentElement;
    const themeVars: Record<string, string | undefined> = {
      '--ls-primary-color': config.primaryColor,
      '--ls-secondary-color': config.secondaryColor,
      '--ls-background-color': config.backgroundColor,
      '--ls-secondary-background-color': config.secondaryBackgroundColor,
      '--ls-primary-text-color': config.primaryTextColor,
      '--ls-secondary-text-color': config.secondaryTextColor,
      '--ls-tertiary-text-color': config.tertiaryTextColor,
      '--ls-font-family': config.fontFamily
    };
    Object.entries(themeVars).forEach(([key, value]) => {
      if (value) {
        root.style.setProperty(key, value);
      }
    });
  }

  getCurrentThemeMode(): ThemeMode {
    return this.currentThemeSubject.value;
  }

  getCurrentThemeConfig(): ThemeConfiguration | null {
    return this.currentThemeConfigSubject.value;
  }

  private applyThemeToDocument(mode: ThemeMode): void {
    const root = document.documentElement;
    
    if (mode === 'dark') {
      this.renderer.addClass(root, 'dark-theme');
      this.renderer.removeClass(root, 'light-theme');
    } else {
      this.renderer.addClass(root, 'light-theme');
      this.renderer.removeClass(root, 'dark-theme');
    }
  }

  // Helper method to get computed color value
  getComputedColor(colorVariable: string): string {
    return getComputedStyle(document.documentElement).getPropertyValue(colorVariable).trim();
  }
} 