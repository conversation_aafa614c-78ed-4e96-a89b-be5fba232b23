<div class="ls-template" id="template-2" [attr.data-clientid]="template.id" [style]="getGlobalStyles()">
  <!-- Header Section -->
  <header class="header-section" *ngIf="headerSection">
    <div class="container header-container">
      <div class="logo">
        <span class="logo-text" data-displayname="Header Logo">{{ headerSection.logo }}</span>
      </div>
      <nav class="navbar">
        <ul class="nav-menu" *ngIf="headerSection.navigation">
          <li *ngFor="let item of headerSection.navigation">
            <a [href]="item.url" class="nav-link" data-displayname="Navigation Link">{{ item.label }}</a>
          </li>
        </ul>
      </nav>
      <button class="cta-button primary" *ngIf="headerSection.ctaButton" data-displayname="Header CTA Button">
        {{ headerSection.ctaButton?.text }}
      </button>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero-section" *ngIf="titleSection">
    <div class="hero-bg" [ngStyle]="{'background-image': 'url(' + titleSection.heroImage + ')'}" data-displayname="Hero Image"></div>
    <div class="hero-overlay"></div>
    <div class="container hero-content">
      <div class="hero-center">
        <h1 class="main-title" data-displayname="Main Title">{{ titleSection.mainTitle }}</h1>
        <p class="subtitle" data-displayname="Subtitle">{{ titleSection.subtitle }}</p>
        <div class="cta-buttons" *ngIf="titleSection.ctaButtons">
          <button *ngFor="let button of titleSection.ctaButtons; let i = index"
                  class="cta-button"
                  [ngClass]="[button.style, button.size]"
                  [attr.data-displayname]="'Hero CTA Button ' + (i + 1)">
            {{ button.text }}
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Calculators Section -->
  <section class="calculators-section" *ngIf="calculatorSection">
    <div class="calculators-container">      
      <div class="coming-soon-overlay">Coming Soon</div>
      <!-- Left: Title, Description, Cards -->
      <div class="calculators-left">
        <div class="calculators-left-inner">
          <h2 class="section-title" data-displayname="Calculators Section Title">{{ calculatorSection.title }}</h2>
          <p class="section-description" data-displayname="Calculators Section Description">{{ calculatorSection.description }}</p>
          <div class="calculators-list" *ngIf="calculatorSection.calculators">
            <div class="calculator-card" *ngFor="let calculator of calculatorSection.calculators; let i = index">
              <div class="calculator-info">
                <h3 class="calculator-title" [attr.data-displayname]="'Calculator ' + (i + 1) + ' Name'">{{ calculator.name }}</h3>
                <p class="calculator-description" [attr.data-displayname]="'Calculator ' + (i + 1) + ' Description'">{{ calculator.description }}</p>
              </div>
              <div class="calculator-arrow">
                <nz-icon nzType="arrow-right" nzTheme="outline"></nz-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Right: Calculator Preview -->
      <div class="calculators-right">
        <div class="calculator-preview">
          <div class="mock-calculator">
            <div class="mock-title">Refinance calculator</div>
            <div class="mock-fields">
              <div class="mock-row">
                <span>Current mortgage balance</span>
                <span>$25304</span>
              </div>
              <div class="mock-row">
                <span>Current Monthly payment</span>
                <span>$25304</span>
              </div>
              <div class="mock-row">
                <span>Current Home Value</span>
                <span>$33041</span>
              </div>
              <div class="mock-row">
                <span>Credit profile</span>
                <span>Select range</span>
              </div>
              <div class="mock-row">
                <span>ZIP Code</span>
                <span>0000</span>
              </div>
            </div>
            <div class="mock-row">
              <span><input type="checkbox" checked disabled style="accent-color: var(--ls-primary-color); margin-right: 8px;"/>Are you a veteran or currently serving in the military</span>
            </div>
            <button class="mock-calc-btn">Calculate</button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="footer-section" *ngIf="footerSection">
    <div class="footer-container">
      <div class="footer-newsletter" *ngIf="footerSection.newsletter">
        <div class="newsletter-title">{{ footerSection.newsletter.title }}</div>
        <div class="newsletter-desc">Be the first to know about our latest updates, exclusive offers, and more.</div>
        <form class="newsletter-form" (submit)="$event.preventDefault()">
          <input type="email" class="newsletter-input" [placeholder]="footerSection.newsletter.placeholder" />
          <button class="newsletter-btn" type="submit">{{ footerSection.newsletter.buttonText }}</button>
        </form>
      </div>
      <div class="footer-links-social">
        <div class="footer-links" *ngIf="footerSection.links">
          <ul class="link-list">
            <li *ngFor="let link of footerSection.links[0].links; let linkIndex = index">
              <a [href]="link.url" class="footer-link" [attr.data-displayname]="'Footer Link ' + (linkIndex + 1)">{{ link.label }}</a>
            </li>
          </ul>
        </div>
        <div class="footer-social" *ngIf="footerSection.socialMedia">
          <a *ngFor="let social of footerSection.socialMedia; let socialIndex = index"
             [href]="social.url"
             class="social-link"
             target="_blank"
             [attr.data-displayname]="'Social Media ' + (socialIndex + 1)">
            <span nz-icon [nzType]="social.icon" class="social-icon"></span>
          </a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-logo">{{ footerSection.companyInfo?.name }}</div>
      <div class="footer-meta">
        <span class="copyright" data-displayname="Copyright Text">{{ footerSection?.copyright }}</span>
        <a *ngIf="footerSection.privacyPolicy" [href]="footerSection.privacyPolicy?.url" class="privacy-link" data-displayname="Privacy Policy Link">{{ footerSection.privacyPolicy?.label }}</a>
      </div>
    </div>
  </footer>
</div> 