#template-2 {
  font-family: var(--ls-font-family, 'Public Sans, sans-serif');
  background: var(--ls-background-color);
  min-height: 100vh;

  // HEADER
  .header-section {
    background: var(--ls-secondary-color);
    color: var(--ls-secondary-background-color);

    .header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32px;
      height: 72px;
      margin: 0 auto;
    }

    .logo-text {
      color: var(--ls-primary-color);
      font-size: 2rem;
      font-weight: 700;
      font-family: inherit;
    }

    .navbar {
      flex: 1;
      display: flex;
      justify-content: center;

      ul.nav-menu {
        display: flex;
        gap: 32px;
        list-style: none;
        margin: 0;
        padding: 0;

        li {
          .nav-link {
            color: var(--ls-secondary-background-color);
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            transition: color 0.2s;

            &:hover {
              color: var(--ls-primary-color);
            }
          }
        }
      }
    }

    .cta-button.primary {
      background: var(--ls-primary-color);
      color: var(--ls-secondary-background-color);
      border: none;
      border-radius: 6px;
      padding: 10px 28px;
      font-size: 1rem;
      font-weight: 600;
      margin-left: 32px;
      transition: background 0.2s;

      &:hover {
        background: #3bb3b8;
      }
    }
  }

  // HERO
  .hero-section {
    position: relative;
    min-height: 420px;
    height: 740px;
    display: flex;
    align-items: center;
    justify-content: center;

    .hero-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-size: cover;
      background-position: center;
      z-index: 1;
      min-height: 420px;
    }

    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(35, 39, 42, 0.7);
      z-index: 2;
      pointer-events: none;
    }

    .hero-content {
      position: relative;
      z-index: 3;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 420px;

      .hero-center {
        text-align: center;
        max-width: 700px;
        margin: 0 auto;

        .main-title {
          color: #fff;
          font-size: 2.5rem;
          font-weight: 700;
          margin-bottom: 16px;
        }

        .subtitle {
          color: #fff;
          font-size: 1.1rem;
          font-weight: 400;
          margin-bottom: 32px;
        }

        .cta-buttons {
          display: flex;
          justify-content: center;
          gap: 16px;

          .cta-button {
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            padding: 12px 28px;
            border: 2px solid var(--ls-primary-color);
            background: transparent;
            color: var(--ls-primary-color);
            transition: all 0.2s;

            &.primary {
              background: var(--ls-primary-color);
              color: #fff;
              border: 2px solid var(--ls-primary-color);

              &:hover {
                background: #3bb3b8;
                border-color: #3bb3b8;
              }
            }

            &.outline {
              background: transparent;
              color: var(--ls-primary-color);
              border: 2px solid var(--ls-primary-color);

              &:hover {
                background: var(--ls-primary-color);
                color: #fff;
              }
            }

            &.large {
              font-size: 1.1rem;
              padding: 14px 36px;
            }
          }
        }
      }
    }
  }

  // CALCULATORS
  .calculators-section {
    background: var(--ls-secondary-background-color);
    padding: 0;
    position: relative;

    .calculators-container {
      display: flex;
      margin: 0 auto;
      padding: 0 0 0 0;
      min-height: 520px;
    }

    .coming-soon-overlay {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      background: #1e1e1eb3;
      pointer-events: all;
      color: #fff;
      font-size: 2rem;
      font-weight: 500;
      letter-spacing: 1px;
      opacity: .4;
    }

    .calculators-left {
      flex: 1;
      padding: 64px 48px 64px 80px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .section-title {
        color: var(--ls-primary-color);
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 12px;
      }

      .section-description {
        color: var(--ls-primary-text-color);
        font-size: 1.1rem;
        margin-bottom: 32px;
        max-width: 420px;
      }

      .calculators-list {
        display: flex;
        flex-direction: column;
        gap: 18px;

        .calculator-card {
          background: var(--ls-secondary-background-color);
          border-radius: 16px;
          box-shadow: 0 2px 12px rgba(35, 39, 42, 0.06);
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 18px 28px;
          transition: box-shadow 0.2s, transform 0.2s;
          cursor: pointer;
          border: 1px solid #e5e5e5;

          &:hover {
            box-shadow: 0 6px 24px rgba(92, 199, 204, 0.15);
            transform: translateY(-2px);
          }

          .calculator-info {
            .calculator-title {
              color: var(--ls-primary-text-color);
              font-size: 1.1rem;
              font-weight: 600;
              margin-bottom: 4px;
            }

            .calculator-description {
              color: var(--ls-secondary-text-color);
              font-size: 0.98rem;
              font-weight: 400;
            }
          }

          .calculator-arrow {
            background: var(--ls-primary-color);
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;

            .anticon {
              color: #fff;
              font-size: 1.3rem;
            }
          }
        }
      }
    }

    .calculators-right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--ls-primary-color);
      position: relative;
      min-width: 420px;

      .calculator-preview {
        width: 380px;
        min-height: 380px;
        background: var(--ls-secondary-background-color);
        border-radius: 18px;
        box-shadow: 0 2px 12px rgba(35, 39, 42, 0.06);
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32px 32px 24px 32px;

        .mock-calculator {
          opacity: 0.5;
          filter: blur(1px);
          pointer-events: none;
          width: 100%;
          margin: 0 auto;

          .mock-title {
            color: var(--ls-primary-text-color);
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 18px;
            text-align: left;
          }

          .mock-fields {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .mock-row {
              display: flex;
              justify-content: space-between;
              color: var(--ls-primary-text-color);
              font-size: 0.98rem;
            }
          }

          .mock-calc-btn {
            margin: 18px 0 0 0;
            background: var(--ls-primary-color);
            color: var(--ls-secondary-background-color);
            border: none;
            border-radius: 6px;
            padding: 10px 32px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
          }
        }
      }
    }
  }

  // FOOTER
  .footer-section {
    background: var(--ls-secondary-background-color);
    padding: 0;

    .footer-container {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin: 0 auto;
      padding: 48px 32px 24px 32px;
      gap: 48px;

      .footer-newsletter {
        flex: 1;

        .newsletter-title {
          color: var(--ls-primary-text-color);
          font-size: 1.1rem;
          font-weight: 700;
          margin-bottom: 12px;
        }

        .newsletter-form {
          display: flex;
          gap: 12px;

          .newsletter-input {
            flex: 1;
            padding: 10px 16px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            font-size: 1rem;
          }

          .newsletter-btn {
            background: var(--ls-primary-color);
            color: var(--ls-secondary-background-color);
            border: none;
            border-radius: 6px;
            padding: 10px 24px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;

            &:hover {
              background: #3bb3b8;
            }
          }
        }
      }

      .footer-links-social {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 24px;

        .footer-links {
          .link-list {
            list-style: none;
            padding: 0;
            margin: 0 0 16px 0;
            display: flex;
            gap: 24px;
            text-decoration: underline;

            li {
              margin-bottom: 8px;

              .footer-link {
                color: var(--ls-primary-text-color);
                font-size: 1rem;
                font-weight: 500;
                text-decoration: none;
                transition: color 0.2s;

                &:hover {
                  color: var(--ls-primary-color);
                }
              }
            }
          }
        }

        .footer-social {
          display: flex;
          gap: 16px;

          .social-link {
            .social-icon {
              color: var(--ls-primary-text-color);
              font-size: 1.5rem;
              background: #eafafd;
              border-radius: 50%;
              padding: 8px;
              transition: background 0.2s, color 0.2s;

              &:hover {
                background: var(--ls-primary-color);
                color: var(--ls-secondary-background-color);
              }
            }
          }
        }
      }
    }

    .footer-bottom {
      margin: 0 auto;
      padding: 0 32px 24px;
      display: flex
  ;
      gap: 48px;
      align-items: center;

      .footer-logo {
        color: var(--ls-primary-color);
        font-size: 1.2rem;
        font-weight: 700;
        flex: 1;
      }

      .footer-meta {
        display: flex
        ;
            align-items: center;
            gap: 18px;
            flex: 1;
            justify-content: space-between;

        .copyright {
          color: var(--ls-secondary-text-color);
          font-size: 1rem;
        }

        .privacy-link {
          color: var(--ls-secondary-text-color);
          font-size: 1rem;
          text-decoration: underline;
          transition: color 0.2s;

          &:hover {
            color: var(--ls-primary-color);
          }
        }
      }
    }
  }

  // RESPONSIVE
  @media (max-width: 1024px) {

    .header-section .header-container,
    .footer-section .footer-container,
    .footer-section .footer-bottom,
    .calculators-section .calculators-container {
      padding-left: 16px;
      padding-right: 16px;
    }

    .calculators-section .calculators-container {
      flex-direction: column;

      .calculators-left,
      .calculators-right {
        padding: 32px 0;
        min-width: 0;
      }

      .calculators-right {
        width: 100%;
        justify-content: flex-start;
      }
    }

    .footer-section .footer-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 32px;
    }

    .footer-section .footer-links-social {
      align-items: flex-start;
    }
  }

  @media (max-width: 600px) {
    .header-section .header-container {
      flex-direction: column;
      height: auto;
      gap: 12px;
      padding: 12px;
    }

    .hero-section .hero-content .hero-center .main-title {
      font-size: 1.5rem;
    }

    .calculators-section .calculators-container {
      padding: 0 4px;
    }

    .footer-section .footer-bottom {
      flex-direction: column;
      gap: 8px;
      padding: 0 8px 16px 8px;
    }
  }
}