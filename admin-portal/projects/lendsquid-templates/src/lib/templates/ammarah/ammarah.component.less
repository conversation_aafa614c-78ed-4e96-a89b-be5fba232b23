@box-shadow-base: 0 2px 8px rgba(0,0,0,0.1);
@border-radius-lg: 16px;

.ammarah-template {
  font-family: var(--ls-font-family);
  color: var(--ls-color-mode-text);
  line-height: 1.5;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .header {
    background-color: var(--ls-color-mode);
    box-shadow: var(--box-shadow-base);
    padding: 16px 0;
    .logo img { height: 40px; }
    .navigation ul {
      display: flex;
      gap: 32px;
      li a {
        color: var(--ls-color-mode-text);
        text-decoration: none;
        font-weight: 500;
        &:hover { color: var(--ls-color-primary); }
      }
    }
  }

  .hero {
    background: var(--ls-color-secondary);
    padding: 120px 0 80px;
    text-align: center;
    h1 { font-size: 48px; font-weight: 700; color: var(--ls-color-mode-text); }
    .subtitle { font-size: 20px; color: var(--ls-color-mode-text); }
  }

  .calculators {
    padding: 80px 0;
    background: var(--ls-color-mode);
    h2 { text-align: center; font-size: 36px; }
    .calculator-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 32px;
    }
    .calculator-card {
      background: var(--ls-color-mode);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--box-shadow-base);
      padding: 24px;
      h3 { font-size: 24px; color: var(--ls-color-mode-text); }
      p { color: var(--ls-color-mode-text); }
    }
  }

  .footer {
    background: var(--ls-color-secondary);
    padding: 80px 0;
    .footer-content {
      display: flex;
      flex-wrap: wrap;
      gap: 48px;
      .company-info, .contact-info, .social-links {
        flex: 1 1 200px;
      }
      .social-icons a {
        color: var(--ls-color-mode-text);
        font-size: 24px;
        &:hover { color: var(--ls-color-primary); }
      }
    }
    .copyright {
      text-align: center;
      margin-top: 32px;
      color: var(--ls-color-mode-text);
    }
  }
} 