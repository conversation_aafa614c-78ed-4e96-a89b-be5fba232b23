<div class="ammarah-template">
  <!-- Header Section -->
  <header class="header">
    <div class="container">
      <div class="logo">
        <img [src]="headerSection.logo" [alt]="headerSection.logoAlt">
      </div>
      <nav class="navigation">
        <ul>
          <li *ngFor="let item of headerSection.navigation">
            <a [href]="item.url">{{ item.label }}</a>
          </li>
        </ul>
      </nav>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <h1>{{ titleSection.mainTitle }}</h1>
      <p class="subtitle">{{ titleSection.subtitle }}</p>
      <div class="cta-buttons">
        <button *ngFor="let button of titleSection.ctaButtons"
                [class]="button.style"
                [class.large]="button.size === 'large'">
          {{ button.text }}
        </button>
      </div>
    </div>
  </section>

  <!-- Calculators Section -->
  <section class="calculators">
    <div class="container">
      <h2>{{ calculatorSection.title }}</h2>
      <p class="section-description">{{ calculatorSection.description }}</p>
      <div class="calculator-grid">
        <div class="calculator-card" *ngFor="let calculator of calculatorSection.calculators">
          <div class="calculator-icon">
            <nz-icon nzType="arrow-down" nzTheme="outline" />
          </div>
          <h3>{{ calculator.name }}</h3>
          <p>{{ calculator.description }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="company-info">
          <h3>{{ footerSection.companyInfo?.name }}</h3>
          <p>{{ footerSection.companyInfo?.description }}</p>
        </div>
        <div class="contact-info">
          <h3>Contact Information</h3>
          <p><i class="icon-location"></i> {{ footerSection.contactInfo?.address }}</p>
          <p><i class="icon-email"></i> {{ footerSection.contactInfo?.email }}</p>
          <p><i class="icon-phone"></i> {{ footerSection.contactInfo?.phone }}</p>
        </div>
        <div class="social-links">
          <h3>Follow Us</h3>
          <div class="social-icons">
            <a *ngFor="let social of footerSection.socialMedia"
               [href]="social.url"
               [title]="social.platform">
              <i [class]="'icon-' + social.icon"></i>
            </a>
          </div>
        </div>
      </div>
      <div class="copyright">
        <p>{{ footerSection.copyright }}</p>
      </div>
    </div>
  </footer>
</div> 