import { Component, OnInit } from '@angular/core';
import { BaseTemplateComponent } from '../../components/base-template/base-template.component';
import { CommonModule } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { ThemeService } from '../../services/theme.service';

@Component({
  selector: 'ls-template-1',
  templateUrl: './template-1.component.html',
  styleUrls: ['./template-1.component.less'],
  imports: [CommonModule, NzIconModule]
})
export class Template1Component extends BaseTemplateComponent implements OnInit {
  constructor(protected override themeService: ThemeService) {
    super(themeService);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }
} 
