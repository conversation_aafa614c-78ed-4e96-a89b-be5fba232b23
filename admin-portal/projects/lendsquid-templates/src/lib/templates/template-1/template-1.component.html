<div class="ls-template" id="template-1" [style]="getGlobalStyles()">
  <!-- Header Section -->
  <header class="header-section" *ngIf="headerSection">
    <div class="container">
      <nav class="navbar">
        <div class="logo">
          <span class="logo-text" data-displayname="Header Logo">{{ headerSection.logo }}</span>
        </div>
        <div class="nav-menu-container">
          <ul class="nav-menu" *ngIf="headerSection.navigation">
            <li *ngFor="let item of headerSection.navigation">
              <a [href]="item.url" class="nav-link" data-displayname="Navigation Link">{{ item.label }}</a>
            </li>
          </ul>

          <button class="cta-button primary" *ngIf="headerSection.ctaButton" data-displayname="Header CTA Button">
            {{ headerSection.ctaButton?.text }}
          </button>
        </div>
      </nav>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero-section" *ngIf="titleSection">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="main-title" data-displayname="Main Title">{{ titleSection.mainTitle }}</h1>
          <p class="subtitle" data-displayname="Subtitle">{{ titleSection.subtitle }}</p>

          <div class="cta-buttons" *ngIf="titleSection.ctaButtons">
            <button *ngFor="let button of titleSection.ctaButtons; let i = index" 
                    class="cta-button" 
                    [class]="button.style"
                    [class.large]="button.size === 'large'"
                    [attr.data-displayname]="'Hero CTA Button ' + (i + 1)">
              {{ button.text }}
            </button>
          </div>
        </div>

        <div class="hero-image" *ngIf="titleSection.heroImage">
          <img [src]="titleSection.heroImage" 
               [alt]="titleSection.heroImageAlt" 
               class="hero-img"
               data-displayname="Hero Image">
        </div>
      </div>
    </div>
  </section>

  <!-- Calculators Section -->
  <section class="calculators-section" *ngIf="calculatorSection">
    <div class="container">
      <div class="coming-soon-overlay">Coming Soon</div>
      <div class="section-header">
        <h2 class="section-title" data-displayname="Calculators Section Title">{{ calculatorSection.title }}</h2>
        <p class="section-description" data-displayname="Calculators Section Description">{{ calculatorSection.description }}</p>
      </div>

      <div class="calculators-grid" *ngIf="calculatorSection.calculators">
        <div class="calculator-card" *ngFor="let calculator of calculatorSection.calculators; let i = index">
          <div class="calculator-content">
            <h3 class="calculator-title" [attr.data-displayname]="'Calculator ' + (i + 1) + ' Name'">{{ calculator.name }}</h3>
            <p class="calculator-description" [attr.data-displayname]="'Calculator ' + (i + 1) + ' Description'">{{ calculator.description }}</p>
          </div>
          <div class="calculator-icon">
            <div class="icon-container">
              <div class="arrow-icon"><nz-icon nzType="arrow-down" nzTheme="outline" /></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="footer-section" *ngIf="footerSection">
    <div class="container">
      <div class="footer-content">
        <!-- Company Info -->
        <div class="company-info" *ngIf="footerSection.companyInfo">
          <h3 class="company-name" data-displayname="Company Name">{{ footerSection.companyInfo?.name }}</h3>
          <p class="company-description" data-displayname="Company Description">{{ footerSection.companyInfo?.description }}</p>
        </div>

        <!-- Links -->
        <ng-container *ngIf="footerSection.links">
          <div class="footer-links" *ngFor="let linkGroup of footerSection.links; let groupIndex = index">
            <ul class="link-list">
              <li *ngFor="let link of linkGroup.links; let linkIndex = index">
                <a [href]="link.url" 
                   class="footer-link" 
                   [attr.data-displayname]="'Footer Link ' + (groupIndex + 1) + '-' + (linkIndex + 1)">
                  {{ link.label }}
                </a>
              </li>
            </ul>
          </div>
        </ng-container>

        <!-- Social Media & Copyright -->
        <div class="footer-connections" *ngIf="footerSection.socialMedia || footerSection.copyright">

          <div class="social-media" *ngIf="footerSection.socialMedia">
            <a *ngFor="let social of footerSection.socialMedia; let socialIndex = index" 
               [href]="social.url" 
               class="social-link" 
               target="_blank"
               [attr.data-displayname]="'Social Media ' + (socialIndex + 1)">
              <span nz-icon [nzType]="social.icon" class="social-icon"></span>
            </a>
          </div>
          <div class="footer-bottom">
            <div class="copyright" *ngIf="footerSection.copyright">
              <span data-displayname="Copyright Text">{{ footerSection.copyright }}</span>
            </div>
            <div class="privacy-policy" *ngIf="footerSection.privacyPolicy">
              <a [href]="footerSection.privacyPolicy?.url" 
                 class="privacy-link"
                 data-displayname="Privacy Policy Link">
                {{ footerSection.privacyPolicy?.label }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>