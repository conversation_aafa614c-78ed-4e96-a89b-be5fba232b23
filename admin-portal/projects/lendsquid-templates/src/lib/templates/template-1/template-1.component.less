#template-1 {
  font-family: var(--ls-font-family, 'Public Sans, sans-serif');
  line-height: 1.6;
  background: var(--ls-background-color);
  padding: 36px 0 0;

  .container {
    margin: 0 auto;
    padding: 0 57.09px;
  }

  // Header Section
  .header-section {
    padding: 8.92px 0;
    background: var(--ls-secondary-background-color);
    border-radius: 66.90px;
    margin: 0 36px;

    .navbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 57.09px;

      .logo {
        .logo-text {
          color: var(--ls-primary-color);
          font-size: 32px;
          font-style: italic;
          font-weight: 700;
          word-wrap: break-word;
        }
      }

      .nav-menu-container {
        display: flex;
        gap: 36px;
        align-items: center;
      }

      .nav-menu {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 0;

        li {
          .nav-link {
            color: var(--ls-secondary-text-color);
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            padding: 20px;
            display: block;
            transition: color 0.3s ease;

            &:hover {
              color: var(--ls-primary-color);
            }
          }
        }
      }

      .cta-button {
        background: var(--ls-primary-color);
        color: #FDFDFD;
        border: none;
        padding: 10px 12px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 700;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          background: var(--ls-secondary-color);
        }
      }
    }
  }

  // Hero Section
  .hero-section {
    padding: 120px 0;

    .hero-content {
      gap: 64px;
      align-items: center;
      display: flex;
      justify-content: space-between;

      .hero-text {
        .main-title {
          color: var(--ls-text-color);
          font-size: 40px;
          font-weight: 500;
          margin-bottom: 32px;
          line-height: 1.2;
          word-wrap: break-word;
        }

        .subtitle {
          color: var(--ls-secondary-text-color);
          font-size: 16px;
          font-weight: 400;
          margin-bottom: 32px;
          line-height: 1.6;
          word-wrap: break-word;
        }

        .cta-buttons {
          display: flex;
          gap: 24px;

          .cta-button {
            padding: 14px 24.98px 13.83px 20.52px;
            border-radius: 42px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;

            &.outline {
              background: var(--ls-secondary-background-color);
              color: var(--ls-text-color);
              border: none;

              &:hover {
                background: var(--ls-background-color);
              }
            }

            &.primary {
              background: var(--ls-primary-color);
              color: #FDFDFD;
              border: none;

              &:hover {
                background: var(--ls-secondary-color);
              }
            }

            &.large {
              padding: 16px 32px;
              font-size: 18px;
            }
          }
        }
      }

      .hero-image {
        .hero-img {
          width: 614.57px;
          height: 512.35px;
          border-radius: 66.90px;
          object-fit: cover;
          background: var(--ls-secondary-background-color);
        }
      }
    }
  }

  // Calculators Section
  .calculators-section {
    position: relative;
    background: var(--ls-secondary-background-color);
    padding: 80px 0;
    border-radius: 77px;
    margin: 0 60px 60px;

    .section-header {
      text-align: center;
      margin-bottom: 64px;

      .section-title {
        color: var(--ls-text-color);
        font-size: 40px;
        font-weight: 500;
        margin-bottom: 16px;
        word-wrap: break-word;
      }

      .section-description {
        color: var(--ls-secondary-text-color);
        font-size: 16px;
        font-weight: 400;
        max-width: 619px;
        margin: 0 auto;
        word-wrap: break-word;
      }
    }

    .calculators-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin: 0 auto;

      .calculator-card {
        background: var(--ls-background-color);
        border-radius: 66.90px;
        padding: 28px 36px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid var(--ls-background-color);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background: #D0D0D0;
          transform: translateY(-2px);
        }

        .calculator-content {
          flex: 1;
          margin-right: 16px;

          .calculator-title {
            color: var(--ls-primary-text-color);
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            word-wrap: break-word;
          }

          .calculator-description {
            color: var(--ls-secondary-text-color);
            font-size: 12px;
            font-weight: 400;
            line-height: 1.4;
            word-wrap: break-word;
          }
        }

        .calculator-icon {
          .icon-container {
            width: 40px;
            height: 40px;
            padding: 8px;
            background: var(--ls-secondary-background-color);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;

            .arrow-icon {
              width: 16.10px;
              height: 8.03px;
              border: 2px solid var(--ls-secondary-color);
              border-left: none;
              border-bottom: none;
              transform: rotate(45deg);
            }
          }
        }
      }
    }

    .coming-soon-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      background: rgba(30, 30, 30, 0.7);
      pointer-events: all;

      span,
      & {
        color: #fff;
        font-size: 2rem;
        font-weight: 500;
        border-radius: 77px;
        letter-spacing: 1px;
        opacity: 0.7;
      }
    }
  }

  // Footer Section
  .footer-section {
    background: var(--ls-secondary-background-color);
    padding: 60px 0;
    margin-top: 0;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 64px;
      margin: 0 auto;

      .company-info {
        .company-name {
          color: var(--ls-primary-color);
          font-size: 32px;
          font-style: italic;
          font-weight: 700;
          margin-bottom: 8px;
          word-wrap: break-word;
        }

        .company-description {
          color: var(--ls-tertiary-text-color);
          font-size: 12px;
          font-weight: 400;
          max-width: 288.67px;
          line-height: 1.6;
          word-wrap: break-word;
        }
      }

      .footer-links {
        .link-list {
          list-style: none;
          padding: 0;
          margin: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 100px;

          li {
            margin-bottom: 4px;

            .footer-link {
              color: var(--ls-secondary-text-color);
              font-size: 16px;
              font-weight: 500;
              text-decoration: underline;
              word-wrap: break-word;
              transition: color 0.3s ease;

              &:hover {
                color: var(--ls-primary-color);
              }
            }
          }
        }
      }

      .footer-connections {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        height: 100px;
        justify-content: space-between;
      }

      .footer-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .copyright {
          color: var(--ls-tertiary-text-color);
          font-size: 14px;
          font-weight: 500;
        }

        .privacy-policy {
          .privacy-link {
            color: var(--ls-tertiary-text-color);
            font-size: 14px;
            font-weight: 500;
            text-decoration: underline;
            word-wrap: break-word;
            transition: color 0.3s ease;

            &:hover {
              color: var(--ls-primary-color);
            }
          }
        }
      }

      .social-media {
        display: flex;
        gap: 20px;

        .social-link {
          .social-icon {
            width: 30px;
            height: 30px;
            background: var(--ls-secondary-background-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: var(--ls-secondary-text-color);
            font-size: 16px;

            &:hover {
              background: var(--ls-primary-color);
              color: white;
              transform: scale(1.1);
            }
          }
        }
      }
    }
  }
}