.lorem-ipsum-template {
  font-family: var(--ls-font-family);
  color: var(--ls-color-mode-text);
  line-height: 1.5;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  // Header Styles
  .header {
    background-color: var(--ls-color-mode);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 16px 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;

    .container {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      img {
        height: 40px;
        width: auto;
      }
    }

    .navigation {
      ul {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 32px;

        li {
          a {
            color: var(--ls-color-mode-text);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;

            &:hover {
              color: var(--ls-color-primary);
            }
          }
        }
      }
    }
  }

  // Title Section Styles
  .title-section {
    background-color: var(--ls-color-secondary);
    padding: 120px 0 80px;
    text-align: center;

    h1 {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 16px;
      color: var(--ls-color-mode-text);
    }

    .subtitle {
      font-size: 20px;
      color: var(--ls-color-mode-text);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  // Calculators Section Styles
  .calculators-section {
    padding: 80px 0;
    background-color: var(--ls-color-mode);

    h2 {
      text-align: center;
      font-size: 36px;
      margin-bottom: 48px;
    }

    .calculators-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 32px;
    }

    .calculator-card {
      background-color: var(--ls-color-mode);
      border-radius: var(--border-radius);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      padding: 24px;
      transition: transform 0.3s;

      &:hover {
        transform: translateY(-4px);
      }

      h3 {
        font-size: 24px;
        margin-bottom: 12px;
        color: var(--ls-color-mode-text);
      }

      p {
        color: var(--ls-color-mode-text);
        margin-bottom: 24px;
      }

      .calculator-form {
        .form-group {
          margin-bottom: 16px;

          label {
            display: block;
            margin-bottom: 8px;
            color: var(--ls-color-mode-text);
          }

          input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius);
            font-size: 16px;

            &:focus {
              outline: none;
              border-color: var(--ls-color-primary);
            }
          }
        }

        .calculate-btn {
          width: 100%;
          padding: 12px;
          background-color: var(--ls-color-primary);
          color: #ffffff;
          border: none;
          border-radius: var(--border-radius);
          font-size: 16px;
          cursor: pointer;
          transition: opacity 0.3s;

          &:hover {
            opacity: 0.9;
          }
        }
      }
    }
  }

  // Contact Section Styles
  .contact-section {
    padding: 80px 0;
    background-color: var(--ls-color-secondary);

    .contact-content {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 48px;
      align-items: center;
    }

    .profile {
      text-align: center;

      img {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        margin-bottom: 16px;
        object-fit: cover;
      }

      h3 {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .position {
        color: var(--ls-color-mode-text);
      }
    }

    .contact-info {
      p {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 12px;

        i {
          color: var(--ls-color-primary);
        }
      }

      .social-links {
        display: flex;
        gap: 16px;
        margin-top: 24px;

        a {
          color: var(--ls-color-mode-text);
          font-size: 24px;
          transition: color 0.3s;

          &:hover {
            color: var(--ls-color-primary);
          }
        }
      }
    }
  }
} 