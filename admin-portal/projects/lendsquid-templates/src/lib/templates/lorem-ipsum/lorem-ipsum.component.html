<div class="lorem-ipsum-template">
  <!-- Header Section -->
  <header class="header">
    <div class="container">
      <div class="logo">
        <img [src]="headerSection.logo" 
             [alt]="headerSection.logoAlt"
             data-displayname="Header Logo">
      </div>
      <nav class="navigation">
        <ul>
          <li *ngFor="let item of headerSection.navigation">
            <a [href]="item.url" data-displayname="Navigation Link">{{ item.label }}</a>
          </li>
        </ul>
      </nav>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <h1 data-displayname="Main Title">{{ titleSection.mainTitle }}</h1>
      <p class="subtitle" data-displayname="Subtitle">{{ titleSection.subtitle }}</p>
      <div class="cta-buttons">
        <button *ngFor="let button of titleSection.ctaButtons"
                [class]="button.style"
                [class.large]="button.size === 'large'"
                data-displayname="CTA Button">
          {{ button.text }}
        </button>
      </div>
    </div>
  </section>

  <!-- Calculators Section -->
  <section class="calculators">
    <div class="container">
      <h2 data-displayname="Calculators Section Title">{{ calculatorSection.title }}</h2>
      <p class="section-description" data-displayname="Calculators Section Description">{{ calculatorSection.description }}</p>
      <div class="calculator-grid">
        <div class="calculator-card" *ngFor="let calculator of calculatorSection.calculators">
          <div class="calculator-icon">
            <nz-icon nzType="arrow-down" nzTheme="outline" />
          </div>
          <h3 data-displayname="Calculator Name">{{ calculator.name }}</h3>
          <p data-displayname="Calculator Description">{{ calculator.description }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="company-info">
          <h3 data-displayname="Company Name">{{ footerSection.companyInfo?.name }}</h3>
          <p data-displayname="Company Description">{{ footerSection.companyInfo?.description }}</p>
        </div>
        <div class="contact-info">
          <h3>Contact Information</h3>
          <p data-displayname="Company Address"><i class="icon-location"></i> {{ footerSection.contactInfo?.address }}</p>
          <p data-displayname="Company Email"><i class="icon-email"></i> {{ footerSection.contactInfo?.email }}</p>
          <p data-displayname="Company Phone"><i class="icon-phone"></i> {{ footerSection.contactInfo?.phone }}</p>
        </div>
        <div class="social-links">
          <h3>Follow Us</h3>
          <div class="social-icons">
            <a *ngFor="let social of footerSection.socialMedia"
               [href]="social.url"
               [title]="social.platform"
               data-displayname="Social Media Link">
              <i [class]="'icon-' + social.icon"></i>
            </a>
          </div>
        </div>
      </div>
      <div class="copyright">
        <p data-displayname="Copyright Text">{{ footerSection.copyright }}</p>
      </div>
    </div>
  </footer>
</div> 