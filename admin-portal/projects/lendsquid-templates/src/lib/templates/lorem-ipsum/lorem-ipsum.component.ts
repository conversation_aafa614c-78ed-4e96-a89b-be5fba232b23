import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { BaseTemplateComponent } from '../../components/base-template/base-template.component';
import { ThemeService } from '../../services/theme.service';

@Component({
  selector: 'ls-lorem-ipsum',
  templateUrl: './lorem-ipsum.component.html',
  styleUrls: ['./lorem-ipsum.component.less'],
  imports: [CommonModule, NzIconModule]
})
export class LoremIpsumComponent extends BaseTemplateComponent implements OnInit {

  constructor(protected override themeService: ThemeService) {
    super(themeService);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }
} 