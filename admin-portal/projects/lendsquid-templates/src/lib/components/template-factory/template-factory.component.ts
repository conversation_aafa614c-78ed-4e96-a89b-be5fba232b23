import { 
  Component, 
  Input, 
  OnInit, 
  OnDestroy, 
  AfterViewInit,
  ViewChild, 
  ViewContainerRef, 
  ComponentRef,
  ChangeDetectionStrategy,
  inject
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Template } from '../../models/template.interface';
import { TemplateLoaderService } from '../../services/template-loader.service';

@Component({
  selector: 'ls-template-factory',
  template: '<ng-container #templateContainer></ng-container>',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TemplateFactoryComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() template!: Template;
  @ViewChild('templateContainer', { read: ViewContainerRef }) container!: ViewContainerRef;
  
  private componentRef: ComponentRef<any> | null = null;
  private templateLoader = inject(TemplateLoaderService);

  constructor() {}

  ngOnInit(): void {
    // Template data is available here, but container is not yet initialized
  }

  ngAfterViewInit(): void {
    if (this.template?.id) {
      this.loadAndRenderTemplate();
    }
  }

  /**
   * Dynamically loads and renders the template component
   */
  private async loadAndRenderTemplate(): Promise<void> {
    try {
      // Clear any existing content
      this.container.clear();
      
      // Load the template component dynamically
      const componentClass = await this.templateLoader.loadTemplate(this.template.id);
      
      // Create and render the component
      this.componentRef = this.container.createComponent(componentClass);
      
      // Pass the template data to the component
      if (this.componentRef.instance) {
        this.componentRef.instance.template = this.template;
      }
      
      console.log(`Template ${this.template.id} loaded and rendered successfully`);
    } catch (error) {
      console.error('Failed to load and render template:', error);
      // You could show an error message or fallback template here
    }
  }

  /**
   * Reloads the template with new data
   * @param newTemplate - The new template data
   */
  async reloadTemplate(newTemplate: Template): Promise<void> {
    this.template = newTemplate;
    await this.loadAndRenderTemplate();
  }

  ngOnDestroy(): void {
    if (this.componentRef) {
      this.componentRef.destroy();
      this.componentRef = null;
    }
  }
} 