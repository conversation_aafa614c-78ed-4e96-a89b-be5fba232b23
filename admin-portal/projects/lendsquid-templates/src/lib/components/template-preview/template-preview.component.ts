import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Template } from '../../models/template.interface';
import { TemplateFactoryComponent } from '../template-factory/template-factory.component';

@Component({
  selector: 'ls-template-preview',
  imports: [CommonModule, TemplateFactoryComponent],
  templateUrl: './template-preview.component.html',
  styleUrls: ['./template-preview.component.less']
})
export class TemplatePreviewComponent implements OnInit {
  @Input() template!: Template;

  constructor() {}

  ngOnInit(): void {
    console.log('Template Preview Component', this.template);
    // Additional initialization if needed
  }
} 