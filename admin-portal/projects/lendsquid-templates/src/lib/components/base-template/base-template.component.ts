import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { CalculatorContent, FooterContent, HeaderContent, Template, TitleContent } from '../../models/template.interface';
import { ThemeService, ThemeMode } from '../../services/theme.service';

@Component({
  selector: 'ls-base-template',
  template: '',
  styles: [],
  imports: [CommonModule]
})
export class BaseTemplateComponent implements OnInit, OnDestroy {
  @Input() template!: Template;
  @Input() themeMode: ThemeMode = 'light';

  private destroy$ = new Subject<void>();

  constructor(protected themeService: ThemeService) { }

  ngOnInit() {
    if (this.template?.theme) {
      this.themeService.setThemeConfiguration(this.template.theme);
    }
    
    this.themeService.setThemeMode(this.themeMode);
    
    // Subscribe to theme changes
    this.themeService.currentTheme$
      .pipe(takeUntil(this.destroy$))
      .subscribe(mode => {
        this.onThemeChange(mode);
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  protected onThemeChange(mode: ThemeMode): void {
    // Override in child components if needed
  }

  get headerSection(): HeaderContent {
    return this.template.sections.find(s => s.type === 'header')?.content as HeaderContent;
  }

  get titleSection(): TitleContent {
    return this.template.sections.find(s => s.type === 'title')?.content as TitleContent;
  }

  get calculatorSection(): CalculatorContent {
    return this.template.sections.find(s => s.type === 'calculators')?.content as CalculatorContent;
  }

  get footerSection(): FooterContent {
    return this.template.sections.find(s => s.type === 'footer')?.content as FooterContent;
  }

  // Helper method to get global styles with theme support
  getGlobalStyles(): string {
    if (!this.template?.theme) return '';
    
    return `
      --ls-primary-color: ${this.template.theme.primaryColor};
      --ls-secondary-color: ${this.template.theme.secondaryColor};
      --ls-background-color: ${this.template.theme.backgroundColor};
      --ls-secondary-background-color: ${this.template.theme.secondaryBackgroundColor};
      --ls-primary-text-color: ${this.template.theme.primaryTextColor};
      --ls-secondary-text-color: ${this.template.theme.secondaryTextColor};
      --ls-tertiary-text-color: ${this.template.theme.tertiaryTextColor};
      --ls-font-family: ${this.template.theme.fontFamily};
    `;
  }
} 