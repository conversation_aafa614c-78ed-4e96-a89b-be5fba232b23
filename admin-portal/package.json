{"name": "admin-portal", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve -o --port 4201", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "build:lib": "ng build lendsquid-templates"}, "private": true, "dependencies": {"@angular/animations": "^20.0.3", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "lendsquid-templates": "file:projects/lendsquid-templates", "ng-zorro-antd": "^20.0.0-next.0", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.0.2", "@angular/cli": "^20.0.2", "@angular/compiler-cli": "^20.0.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "less": "^4.2.0", "ng-packagr": "^20.0.0", "typescript": "~5.8.2"}}