{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"admin-portal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "less", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "src/styles.less"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3MB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "32kB", "maximumError": "64kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "admin-portal:build:production"}, "development": {"buildTarget": "admin-portal:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "less", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.less"]}}}}, "lendsquid-templates": {"projectType": "library", "root": "projects/lendsquid-templates", "sourceRoot": "projects/lendsquid-templates/src", "prefix": "ls", "architect": {"build": {"builder": "@angular/build:ng-packagr", "configurations": {"production": {"tsConfig": "projects/lendsquid-templates/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/lendsquid-templates/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "projects/lendsquid-templates/tsconfig.spec.json"}}}}}}