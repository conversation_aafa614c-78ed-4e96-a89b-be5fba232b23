/* You can add global styles to this file, and also import other style files */
@import './styles/variables.less';

/* Global styles */
* {
  font-family: 'Nunito Sans', sans-serif;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Nunito Sans', sans-serif;
  font-size: @font-size-base;
  line-height: @line-height-base;
  color: var(--text-color);
  background-color: var(--neutral-1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* lendsquid-templates */
:root {
  --ls-color-primary: #850E35;
  --ls-color-secondary: #EE6983;
  --ls-color-mode: #7ca399e5;
  --ls-color-mode-text: #170fceff;
  --ls-font-family: 'Nunito Sans', sans-serif;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: @margin-xs; }
.mb-2 { margin-bottom: @margin-sm; }
.mb-3 { margin-bottom: @margin-md; }
.mb-4 { margin-bottom: @margin-lg; }
.mb-5 { margin-bottom: @margin-xl; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: @margin-xs; }
.mt-2 { margin-top: @margin-sm; }
.mt-3 { margin-top: @margin-md; }
.mt-4 { margin-top: @margin-lg; }
.mt-5 { margin-top: @margin-xl; }

.p-0 { padding: 0; }
.p-1 { padding: @padding-xs; }
.p-2 { padding: @padding-sm; }
.p-3 { padding: @padding-md; }
.p-4 { padding: @padding-lg; }
.p-5 { padding: @padding-xl; }

/* Responsive utilities */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

@media (max-width: @screen-sm) {
  .d-sm-none { display: none; }
  .d-sm-block { display: block; }
  .d-sm-flex { display: flex; }
}

@media (max-width: @screen-md) {
  .d-md-none { display: none; }
  .d-md-block { display: block; }
  .d-md-flex { display: flex; }
}
