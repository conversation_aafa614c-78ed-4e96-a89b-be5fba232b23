import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ThemeService } from './core/services/theme.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.html',
  styleUrl: './app.less'
})
export class App implements OnInit {
  protected title = 'admin-portal';

  constructor(private themeService: ThemeService) {}

  ngOnInit(): void {
    // Initialize theme based on system preference if no stored preference
    this.themeService.initializeTheme();
  }
}
