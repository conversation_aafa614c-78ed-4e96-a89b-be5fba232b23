import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./features/landing-page/landing-page.component').then(m => m.LandingPageComponent)
  },
  {
    path: 'templates',
    loadComponent: () => import('./features/template-selection/template-selection').then(m => m.TemplateSelectionComponent),
  },
  {
    path: 'templates/preview/:id',
    loadComponent: () => import('./features/template-preview/template-preview-page.component').then(m => m.TemplatePreviewPageComponent)
  },
  {
    path: 'templates/customize/:id',
    loadComponent: () => import('./features/template-customization/template-customization').then(m => m.TemplateCustomizationComponent),
    // canActivate: [AuthGuard]
  },
  {
    path: '**',
    redirectTo: ''
  }
];
