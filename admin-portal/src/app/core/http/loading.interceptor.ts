import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LoadingService } from '../services/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {

  constructor(private loadingService: LoadingService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>andler): Observable<HttpEvent<any>> {
    // Skip loading for certain requests
    if (this.shouldSkipLoading(request)) {
      return next.handle(request);
    }

    // Show loading
    this.loadingService.show();

    return next.handle(request).pipe(
      finalize(() => {
        // Hide loading when request completes
        this.loadingService.hide();
      })
    );
  }

  private shouldSkipLoading(request: HttpRequest<any>): boolean {
    // Skip loading for specific endpoints or request types
    const skipLoadingEndpoints = [
      '/auth/refresh',
      '/health',
      '/ping'
    ];

    // Skip loading for requests with specific headers
    if (request.headers.has('X-Skip-Loading')) {
      return true;
    }

    // Skip loading for specific endpoints
    return skipLoadingEndpoints.some(endpoint => 
      request.url.includes(endpoint)
    );
  }
}
