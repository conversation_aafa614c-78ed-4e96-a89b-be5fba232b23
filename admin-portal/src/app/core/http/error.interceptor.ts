import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { NotificationService } from '../services/notification.service';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {

  constructor(private notificationService: NotificationService) {}

  intercept(request: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        let errorMessage = 'An unexpected error occurred';
        let errorTitle = 'Error';

        if (error.error instanceof ErrorEvent) {
          // Client-side error
          errorMessage = error.error.message;
          errorTitle = 'Client Error';
        } else {
          // Server-side error
          switch (error.status) {
            case 400:
              errorTitle = 'Bad Request';
              errorMessage = error.error?.message || 'Invalid request data';
              break;
            case 401:
              errorTitle = 'Unauthorized';
              errorMessage = 'Please log in to continue';
              break;
            case 403:
              errorTitle = 'Forbidden';
              errorMessage = 'You do not have permission to perform this action';
              break;
            case 404:
              errorTitle = 'Not Found';
              errorMessage = 'The requested resource was not found';
              break;
            case 422:
              errorTitle = 'Validation Error';
              errorMessage = this.formatValidationErrors(error.error?.errors) || 'Validation failed';
              break;
            case 500:
              errorTitle = 'Server Error';
              errorMessage = 'Internal server error. Please try again later';
              break;
            case 503:
              errorTitle = 'Service Unavailable';
              errorMessage = 'Service is temporarily unavailable. Please try again later';
              break;
            default:
              errorTitle = `Error ${error.status}`;
              errorMessage = error.error?.message || error.message || 'An unexpected error occurred';
          }
        }

        // Don't show notification for 401 errors (handled by auth interceptor)
        if (error.status !== 401) {
          this.notificationService.error(errorTitle, errorMessage);
        }

        return throwError(() => error);
      })
    );
  }

  private formatValidationErrors(errors: any): string {
    if (!errors) return '';
    
    if (typeof errors === 'string') return errors;
    
    if (Array.isArray(errors)) {
      return errors.join(', ');
    }
    
    if (typeof errors === 'object') {
      const errorMessages: string[] = [];
      Object.keys(errors).forEach(key => {
        if (Array.isArray(errors[key])) {
          errorMessages.push(...errors[key]);
        } else {
          errorMessages.push(errors[key]);
        }
      });
      return errorMessages.join(', ');
    }
    
    return 'Validation failed';
  }
}
