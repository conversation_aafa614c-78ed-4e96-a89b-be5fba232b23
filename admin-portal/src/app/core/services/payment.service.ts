import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface PaymentPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  isPopular?: boolean;
  stripePriceId?: string;
  comingSoon?: boolean;
}

export interface PaymentSession {
  id: string;
  url: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  stripeSubscriptionId?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card';
  card: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
  isDefault: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private readonly baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get available payment plans
   */
  getPaymentPlans(): Observable<PaymentPlan[]> {
    return of(this.getMockPaymentPlans())
  }

  /**
   * Create Stripe checkout session
   */
  createCheckoutSession(planId: string, websiteId?: string, customDomain?: string): Observable<PaymentSession> {
    const payload = {
      planId,
      websiteId,
      customDomain,
      successUrl: `${window.location.origin}/payment/success`,
      cancelUrl: `${window.location.origin}/template-customization`
    };

    return this.http.post<PaymentSession>(`${this.baseUrl}/api/payment/create-checkout-session`, payload)
      .pipe(
        catchError(() => {
          // Mock response for development
          return of({
            id: 'mock-session-' + Date.now(),
            url: 'https://checkout.stripe.com/mock-session',
            status: 'pending' as const
          }).pipe(delay(1000));
        })
      );
  }

  /**
   * Get user's current subscription
   */
  getUserSubscription(userId: string): Observable<Subscription | null> {
    return this.http.get<Subscription>(`${this.baseUrl}/api/payment/subscription/${userId}`)
      .pipe(
        catchError(() => of(null))
      );
  }

  /**
   * Cancel subscription
   */
  cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Observable<Subscription> {
    return this.http.post<Subscription>(`${this.baseUrl}/api/payment/subscription/${subscriptionId}/cancel`, {
      cancelAtPeriodEnd
    });
  }

  /**
   * Resume subscription
   */
  resumeSubscription(subscriptionId: string): Observable<Subscription> {
    return this.http.post<Subscription>(`${this.baseUrl}/api/payment/subscription/${subscriptionId}/resume`, {});
  }

  /**
   * Update subscription plan
   */
  updateSubscriptionPlan(subscriptionId: string, newPlanId: string): Observable<Subscription> {
    return this.http.put<Subscription>(`${this.baseUrl}/api/payment/subscription/${subscriptionId}`, {
      planId: newPlanId
    });
  }

  /**
   * Get payment methods
   */
  getPaymentMethods(userId: string): Observable<PaymentMethod[]> {
    return this.http.get<PaymentMethod[]>(`${this.baseUrl}/api/payment/payment-methods/${userId}`)
      .pipe(
        catchError(() => of([]))
      );
  }

  /**
   * Add payment method
   */
  addPaymentMethod(userId: string, paymentMethodId: string): Observable<PaymentMethod> {
    return this.http.post<PaymentMethod>(`${this.baseUrl}/api/payment/payment-methods`, {
      userId,
      paymentMethodId
    });
  }

  /**
   * Remove payment method
   */
  removePaymentMethod(paymentMethodId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/api/payment/payment-methods/${paymentMethodId}`);
  }

  /**
   * Set default payment method
   */
  setDefaultPaymentMethod(userId: string, paymentMethodId: string): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}/api/payment/payment-methods/${paymentMethodId}/default`, {
      userId
    });
  }

  /**
   * Get payment history
   */
  getPaymentHistory(userId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/api/payment/history/${userId}`)
      .pipe(
        catchError(() => of([]))
      );
  }

  /**
   * Verify payment session
   */
  verifyPaymentSession(sessionId: string): Observable<{ success: boolean; subscription?: Subscription }> {
    return this.http.get<{ success: boolean; subscription?: Subscription }>
      (`${this.baseUrl}/api/payment/verify-session/${sessionId}`)
      .pipe(
        catchError(() => of({ success: false }))
      );
  }

  /**
   * Calculate prorated amount for plan change
   */
  calculateProration(currentPlanId: string, newPlanId: string): Observable<{ amount: number; currency: string }> {
    return this.http.post<{ amount: number; currency: string }>(`${this.baseUrl}/api/payment/calculate-proration`, {
      currentPlanId,
      newPlanId
    }).pipe(
      catchError(() => of({ amount: 0, currency: 'usd' }))
    );
  }

  /**
   * Get mock payment plans for development
   */
  private getMockPaymentPlans(): PaymentPlan[] {
    return [
      {
        id: 'free',
        name: 'Free',
        description: 'Get started with basic features',
        price: 0,
        currency: 'usd',
        interval: 'month',
        features: [
          'LendSquid subdomain',
          'Basic templates',
          'Email support',
          'Basic analytics'
        ],
        comingSoon: true
      },
      {
        id: 'premium',
        name: 'Premium',
        description: 'Advanced features for growing businesses',
        price: 29.99,
        currency: 'usd',
        interval: 'month',
        features: [
          'Custom domain',
          'All templates',
          'Priority support',
          'Advanced analytics',
          'Lead capture forms',
          'CRM integrations'
        ],
        isPopular: true,
        stripePriceId: 'price_premium_monthly'
      }
    ];
  }

  /**
   * Redirect to Stripe checkout
   */
  redirectToCheckout(sessionId: string): void {
    // In a real implementation, you would use Stripe.js
    // For now, we'll simulate the redirect
    console.log('Redirecting to Stripe checkout with session:', sessionId);
    
    // Mock redirect for development
    if (environment.production) {
      window.location.href = `https://checkout.stripe.com/pay/${sessionId}`;
    } else {
      // Simulate successful payment after 3 seconds
      setTimeout(() => {
        window.location.href = '/payment/success?session_id=' + sessionId;
      }, 3000);
    }
  }
}
