import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type Theme = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'lendsquid-theme';
  private readonly DEFAULT_THEME: Theme = 'light';
  
  private themeSubject = new BehaviorSubject<Theme>(this.getStoredTheme());
  public theme$: Observable<Theme> = this.themeSubject.asObservable();

  constructor() {
    this.applyTheme(this.themeSubject.value);
  }

  /**
   * Get the current theme
   */
  get currentTheme(): Theme {
    return this.themeSubject.value;
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    const newTheme: Theme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  /**
   * Set a specific theme
   */
  setTheme(theme: Theme): void {
    if (this.currentTheme !== theme) {
      this.themeSubject.next(theme);
      this.applyTheme(theme);
      this.storeTheme(theme);
    }
  }

  /**
   * Apply theme to the document
   */
  private applyTheme(theme: Theme): void {
    const documentElement = document.documentElement;
    
    if (theme === 'dark') {
      documentElement.setAttribute('data-theme', 'dark');
      documentElement.classList.add('dark-theme');
      documentElement.classList.remove('light-theme');
    } else {
      documentElement.removeAttribute('data-theme');
      documentElement.classList.add('light-theme');
      documentElement.classList.remove('dark-theme');
    }

    // Update Ng-Zorro theme
    this.updateNgZorroTheme(theme);
  }

  /**
   * Update Ng-Zorro theme configuration
   */
  private updateNgZorroTheme(theme: Theme): void {
    // This will be handled by the global Ng-Zorro configuration
    // The theme change will be reflected through CSS variables
  }

  /**
   * Get stored theme from localStorage
   */
  private getStoredTheme(): Theme {
    try {
      const stored = localStorage.getItem(this.THEME_KEY);
      return (stored === 'dark' || stored === 'light') ? stored : this.DEFAULT_THEME;
    } catch {
      return this.DEFAULT_THEME;
    }
  }

  /**
   * Store theme in localStorage
   */
  private storeTheme(theme: Theme): void {
    try {
      localStorage.setItem(this.THEME_KEY, theme);
    } catch (error) {
      console.warn('Failed to store theme preference:', error);
    }
  }

  /**
   * Check if the system prefers dark mode
   */
  isSystemDarkMode(): boolean {
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  }

  /**
   * Initialize theme based on system preference if no stored preference
   */
  initializeTheme(): void {
    const storedTheme = this.getStoredTheme();
    if (storedTheme === this.DEFAULT_THEME && this.isSystemDarkMode()) {
      this.setTheme('dark');
    }
  }
} 