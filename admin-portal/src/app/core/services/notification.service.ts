import { Injectable } from '@angular/core';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';

export type NotificationType = 'success' | 'info' | 'warning' | 'error';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor(
    private nzNotification: NzNotificationService,
    private nzMessage: NzMessageService
  ) {}

  /**
   * Show success notification
   */
  public success(title: string, content?: string, duration: number = 4500): void {
    this.nzNotification.success(title, content || '', {
      nzDuration: duration,
      nzPlacement: 'topRight'
    });
  }

  /**
   * Show info notification
   */
  public info(title: string, content?: string, duration: number = 4500): void {
    this.nzNotification.info(title, content || '', {
      nzDuration: duration,
      nzPlacement: 'topRight'
    });
  }

  /**
   * Show warning notification
   */
  public warning(title: string, content?: string, duration: number = 4500): void {
    this.nzNotification.warning(title, content || '', {
      nzDuration: duration,
      nzPlacement: 'topRight'
    });
  }

  /**
   * Show error notification
   */
  public error(title: string, content?: string, duration: number = 6000): void {
    this.nzNotification.error(title, content || '', {
      nzDuration: duration,
      nzPlacement: 'topRight'
    });
  }

  /**
   * Show simple message
   */
  public message(type: NotificationType, content: string, duration: number = 3000): void {
    switch (type) {
      case 'success':
        this.nzMessage.success(content, { nzDuration: duration });
        break;
      case 'info':
        this.nzMessage.info(content, { nzDuration: duration });
        break;
      case 'warning':
        this.nzMessage.warning(content, { nzDuration: duration });
        break;
      case 'error':
        this.nzMessage.error(content, { nzDuration: duration });
        break;
    }
  }

  /**
   * Show loading message
   */
  public loading(content: string): string {
    return this.nzMessage.loading(content, { nzDuration: 0 }).messageId;
  }

  /**
   * Remove message by ID
   */
  public removeMessage(messageId: string): void {
    this.nzMessage.remove(messageId);
  }

  /**
   * Remove all messages
   */
  public removeAllMessages(): void {
    this.nzMessage.remove();
  }

  /**
   * Remove all notifications
   */
  public removeAllNotifications(): void {
    this.nzNotification.remove();
  }
}
