import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay, map, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface ZapierWebhook {
  id: string;
  name: string;
  url: string;
  isActive: boolean;
  events: string[];
  createdAt: Date;
  lastTriggered?: Date;
}

export interface GoogleSheetsIntegration {
  id: string;
  name: string;
  spreadsheetId: string;
  spreadsheetUrl: string;
  worksheetName: string;
  isActive: boolean;
  fieldMapping: { [key: string]: string };
  createdAt: Date;
  lastSync?: Date;
}

export interface IntegrationEvent {
  id: string;
  type: 'lead_capture' | 'form_submission' | 'calculator_use' | 'page_view';
  data: any;
  timestamp: Date;
  status: 'pending' | 'sent' | 'failed';
  retryCount?: number;
}

export interface ZapierConnection {
  id: string;
  userId: string;
  websiteId: string;
  type: 'google_sheets' | 'webhook' | 'email' | 'crm';
  name: string;
  configuration: any;
  isActive: boolean;
  createdAt: Date;
  lastUsed?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class ZapierService {
  private readonly baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Create Zapier webhook
   */
  createWebhook(websiteId: string, webhookData: Partial<ZapierWebhook>): Observable<ZapierWebhook> {
    return this.http.post<ZapierWebhook>(`${this.baseUrl}/api/integrations/zapier/webhooks`, {
      websiteId,
      ...webhookData
    }).pipe(
      catchError(() => {
        // Mock response for development
        return of({
          id: 'webhook-' + Date.now(),
          name: webhookData.name || 'New Webhook',
          url: webhookData.url || 'https://hooks.zapier.com/hooks/catch/mock',
          isActive: true,
          events: webhookData.events || ['lead_capture'],
          createdAt: new Date()
        }).pipe(delay(1000));
      })
    );
  }

  /**
   * Get webhooks for a website
   */
  getWebhooks(websiteId: string): Observable<ZapierWebhook[]> {
    return this.http.get<ZapierWebhook[]>(`${this.baseUrl}/api/integrations/zapier/webhooks/${websiteId}`)
      .pipe(
        catchError(() => of(this.getMockWebhooks()))
      );
  }

  /**
   * Update webhook
   */
  updateWebhook(webhookId: string, updates: Partial<ZapierWebhook>): Observable<ZapierWebhook> {
    return this.http.put<ZapierWebhook>(`${this.baseUrl}/api/integrations/zapier/webhooks/${webhookId}`, updates);
  }

  /**
   * Delete webhook
   */
  deleteWebhook(webhookId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/api/integrations/zapier/webhooks/${webhookId}`);
  }

  /**
   * Test webhook
   */
  testWebhook(webhookId: string, testData?: any): Observable<{ success: boolean; response?: any; error?: string }> {
    return this.http.post<{ success: boolean; response?: any; error?: string }>
      (`${this.baseUrl}/api/integrations/zapier/webhooks/${webhookId}/test`, testData || {})
      .pipe(
        catchError(() => of({ success: true, response: 'Test successful' }).pipe(delay(2000)))
      );
  }

  /**
   * Create Google Sheets integration
   */
  createGoogleSheetsIntegration(websiteId: string, integrationData: Partial<GoogleSheetsIntegration>): Observable<GoogleSheetsIntegration> {
    return this.http.post<GoogleSheetsIntegration>(`${this.baseUrl}/api/integrations/google-sheets`, {
      websiteId,
      ...integrationData
    }).pipe(
      catchError(() => {
        // Mock response for development
        return of({
          id: 'sheets-' + Date.now(),
          name: integrationData.name || 'Lead Capture Sheet',
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          spreadsheetUrl: 'https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          worksheetName: 'Leads',
          isActive: true,
          fieldMapping: {
            'name': 'A',
            'email': 'B',
            'phone': 'C',
            'message': 'D',
            'timestamp': 'E'
          },
          createdAt: new Date()
        }).pipe(delay(1000));
      })
    );
  }

  /**
   * Get Google Sheets integrations
   */
  getGoogleSheetsIntegrations(websiteId: string): Observable<GoogleSheetsIntegration[]> {
    return this.http.get<GoogleSheetsIntegration[]>(`${this.baseUrl}/api/integrations/google-sheets/${websiteId}`)
      .pipe(
        catchError(() => of([]))
      );
  }

  /**
   * Update Google Sheets integration
   */
  updateGoogleSheetsIntegration(integrationId: string, updates: Partial<GoogleSheetsIntegration>): Observable<GoogleSheetsIntegration> {
    return this.http.put<GoogleSheetsIntegration>(`${this.baseUrl}/api/integrations/google-sheets/${integrationId}`, updates);
  }

  /**
   * Test Google Sheets integration
   */
  testGoogleSheetsIntegration(integrationId: string, testData?: any): Observable<{ success: boolean; rowAdded?: number; error?: string }> {
    return this.http.post<{ success: boolean; rowAdded?: number; error?: string }>
      (`${this.baseUrl}/api/integrations/google-sheets/${integrationId}/test`, testData || {})
      .pipe(
        catchError(() => of({ success: true, rowAdded: 42 }).pipe(delay(2000)))
      );
  }

  /**
   * Send data to webhook
   */
  sendToWebhook(webhookUrl: string, data: any): Observable<{ success: boolean; response?: any }> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<any>(webhookUrl, data, { headers })
      .pipe(
        map(response => ({ success: true, response })),
        catchError(error => of({ success: false, response: error }))
      );
  }

  /**
   * Get integration events/logs
   */
  getIntegrationEvents(websiteId: string, limit: number = 50): Observable<IntegrationEvent[]> {
    return this.http.get<IntegrationEvent[]>(`${this.baseUrl}/api/integrations/events/${websiteId}?limit=${limit}`)
      .pipe(
        catchError(() => of(this.getMockIntegrationEvents()))
      );
  }

  /**
   * Get all integrations for a website
   */
  getWebsiteIntegrations(websiteId: string): Observable<ZapierConnection[]> {
    return this.http.get<ZapierConnection[]>(`${this.baseUrl}/api/integrations/${websiteId}`)
      .pipe(
        catchError(() => of([]))
      );
  }

  /**
   * Create new integration connection
   */
  createIntegration(websiteId: string, integration: Partial<ZapierConnection>): Observable<ZapierConnection> {
    return this.http.post<ZapierConnection>(`${this.baseUrl}/api/integrations`, {
      websiteId,
      ...integration
    });
  }

  /**
   * Delete integration
   */
  deleteIntegration(integrationId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/api/integrations/${integrationId}`);
  }

  /**
   * Toggle integration status
   */
  toggleIntegration(integrationId: string, isActive: boolean): Observable<ZapierConnection> {
    return this.http.patch<ZapierConnection>(`${this.baseUrl}/api/integrations/${integrationId}`, { isActive });
  }

  /**
   * Get available integration templates
   */
  getIntegrationTemplates(): Observable<any[]> {
    return of([
      {
        id: 'google-sheets-leads',
        name: 'Google Sheets Lead Capture',
        description: 'Automatically add new leads to a Google Sheets spreadsheet',
        type: 'google_sheets',
        icon: 'file-excel',
        fields: ['name', 'email', 'phone', 'message', 'source']
      },
      {
        id: 'zapier-webhook',
        name: 'Zapier Webhook',
        description: 'Send data to any Zapier webhook for custom automation',
        type: 'webhook',
        icon: 'api',
        fields: ['customizable']
      },
      {
        id: 'email-notifications',
        name: 'Email Notifications',
        description: 'Send email notifications when forms are submitted',
        type: 'email',
        icon: 'mail',
        fields: ['to', 'subject', 'template']
      }
    ]);
  }

  /**
   * Mock data for development
   */
  private getMockWebhooks(): ZapierWebhook[] {
    return [
      {
        id: 'webhook-1',
        name: 'Lead Capture Webhook',
        url: 'https://hooks.zapier.com/hooks/catch/123456/abcdef/',
        isActive: true,
        events: ['lead_capture', 'form_submission'],
        createdAt: new Date('2024-01-15'),
        lastTriggered: new Date('2024-01-20')
      }
    ];
  }

  private getMockIntegrationEvents(): IntegrationEvent[] {
    return [
      {
        id: 'event-1',
        type: 'lead_capture',
        data: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          source: 'mortgage_calculator'
        },
        timestamp: new Date('2024-01-20T10:30:00Z'),
        status: 'sent'
      },
      {
        id: 'event-2',
        type: 'form_submission',
        data: {
          name: 'Jane Smith',
          email: '<EMAIL>',
          message: 'Interested in HELOC options'
        },
        timestamp: new Date('2024-01-20T09:15:00Z'),
        status: 'sent'
      }
    ];
  }
}
