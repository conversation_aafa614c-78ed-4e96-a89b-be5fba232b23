import { Template } from 'lendsquid-templates';
import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, forkJoin, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { map, switchMap, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TemplateService {
  private selectedTemplateSubject = new BehaviorSubject<Template | null>(null);
  public selectedTemplate$ = this.selectedTemplateSubject.asObservable();

  constructor(
    private http: HttpClient
  ) { }

  getTemplates(): Observable<Template[]> {
    return this.http.get<string[]>('assets/templates/template-list.json').pipe(
      map(files => files || []), // Ensure we have an array
      switchMap(files => {
        if (files.length === 0) {
          return of([]);
        }
        return forkJoin(
          files.map(file =>
            this.http.get<Template>(`assets/templates/${file}/template.json`)
          )
        );
      })
    );
  }

  getTemplateById(id: string): Observable<Template | null> {
    return this.getTemplates().pipe(
      map(templates => templates.find(template => template.id === id) || null),
      tap(template => {
        if (!template) {
          console.error(`Template with ID ${id} not found`);
        }
      })
    );
  }

  setSelectedTemplate(template: Template): void {
    this.selectedTemplateSubject.next(template);
  }

  getSelectedTemplate(): Template | null {
    return this.selectedTemplateSubject.value;
  }
}
