// Landing Page Styles
@import '../../../styles/variables.less';

.landing-page {
  width: 100%;
  overflow-x: hidden;

  // Container
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    @media (max-width: 768px) {
      padding: 0 16px;
    }
  }

  // Common section styles
  section {
    padding: 80px 0;

    @media (max-width: 768px) {
      padding: 60px 0;
    }
  }

  // Typography
  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-color);
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 2rem;
    }

    @media (max-width: 480px) {
      font-size: 1.75rem;
    }
  }

  .section-subtitle {
    font-size: 1.25rem;
    text-align: center;
    margin-bottom: 40px;
    color: var(--text-color-secondary);
    line-height: 1.6;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      font-size: 1.1rem;
    }
  }

  // CTA Button
  .cta-button {
    background: linear-gradient(135deg, #1890ff, #722ed1);
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    padding: 12px 32px;
    height: auto;
    box-shadow: 0 4px 15px rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
    }

    @media (max-width: 768px) {
      font-size: 1rem;
      padding: 10px 24px;
    }
  }

  .cta-section {
    text-align: center;
    margin-top: 60px;

    @media (max-width: 768px) {
      margin-top: 40px;
    }
  }

  // Hero Section
  .hero-section {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 100px 0;
    text-align: center;
    position: relative;

    @media (max-width: 768px) {
      padding: 80px 0;
    }
  }

  .hero-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
  }

  .theme-toggle-container {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;

    @media (max-width: 768px) {
      top: 10px;
      right: 10px;
    }
  }

  .hero-title {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-color);
    margin-bottom: 40px;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 2.25rem;
    }

    @media (max-width: 480px) {
      font-size: 1.875rem;
    }
  }

  .hero-image {
    margin: 40px 0;
    text-align: center;
  }

  .hero-gif {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  // AI Chatbot Style Input
  .ai-input-container {
    max-width: 600px;
    margin: 40px auto;
    padding: 0 20px;

    @media (max-width: 768px) {
      margin: 30px auto;
      padding: 0 16px;
    }
  }

  .input-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;
  }

  .ai-icon {
    font-size: 20px;
    color: #722ed1;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .input-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color-secondary);
  }

  .ai-form {
    width: 100%;
  }

  .input-field-container {
    margin-bottom: 24px;
    max-width: 512px;
    box-shadow: 0px 0px 1px 16px rgba(92, 51, 255, 0.1), 0px 0px 0px 8px rgba(92, 51, 255, 0.18);
    border: 2px solid rgba(92, 51, 255, 1);
    border-radius: 12px;
    background: white;
  }

  .ai-textarea {
    flex: 1;
    border: none;
    border-radius: 12px;
    padding: 16px 20px;
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    transition: all 0.3s ease;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &:focus {
      border-color: #722ed1;
      box-shadow: 0 0 0 3px rgba(114, 46, 209, 0.1);
      outline: none;
    }

    &::placeholder {
      color: #bfbfbf;
      font-style: italic;
    }

    @media (max-width: 768px) {
      padding: 14px 16px;
      font-size: 15px;
      border-radius: 14px;
    }
  }

  .send-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(114, 46, 209, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 2px 8px rgba(114, 46, 209, 0.2);
    }

    .anticon {
      font-size: 18px;
      color: #fff;
    }

    @media (max-width: 768px) {
      width: 44px;
      height: 44px;

      .anticon {
        font-size: 16px;
      }
    }
  }

  .validation-message {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #f5222d;
    font-size: 13px;
    margin-bottom: 12px;
    padding-left: 4px;

    .error-icon {
      font-size: 14px;
    }
  }

  .input-suggestions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 8px;
  }

  .suggestion-text {
    font-size: 12px;
    color: var(--text-color-secondary);
    font-weight: 500;
  }

  .suggestion-chip {
    background: #f0f9ff;
    border: 1px solid #e0f2fe;
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 12px;
    color: #1890ff;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #e0f2fe;
      border-color: #1890ff;
      transform: translateY(-1px);
    }

    @media (max-width: 768px) {
      padding: 6px 10px;
      font-size: 11px;
    }
  }

  .cta-divider {
    text-align: center;
    margin: 30px 0;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, #e8e8e8, transparent);
    }

    span {
      background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
      padding: 8px 20px;
      border-radius: 20px;
      font-size: 14px;
      color: var(--text-color-secondary);
      font-weight: 500;
      position: relative;
      z-index: 1;
    }

    @media (max-width: 768px) {
      margin: 24px 0;

      span {
        padding: 6px 16px;
        font-size: 13px;
      }
    }
  }

  // Value Proposition Section
  .value-prop-section {
    background: #fff;
    text-align: center;
  }

  .value-prop-image {
    margin-top: 40px;
    text-align: center;
  }

  .demo-gif {
    max-width: 100%;
    height: auto;
  }

  // How It Works Section
  .how-it-works-section {
    background: #fafafa;
  }

  .steps-grid,
  .blog-posts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 30px;
    }
  }

  .step-card,
  .blog-post {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }
  }

  .step-image {
    margin-bottom: 20px;
  }

  .step-gif {
    width: 100%;
    max-width: 200px;
    height: auto;
    border-radius: 8px;
  }

  .step-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
  }

  .step-divider {
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    margin: 0 auto 15px;
    border-radius: 2px;
  }

  .step-description {
    color: var(--text-color-secondary);
    line-height: 1.6;
    font-size: 1rem;
  }

  // Benefits Grid
  .benefits-grid,
  .agent-benefits-grid {
    display: grid;
    gap: 60px;
    margin-top: 60px;

    @media (max-width: 768px) {
      gap: 40px;
    }
  }

  .benefit-card,
  .agent-benefit-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);

    &:nth-child(even) {

      .benefit-content,
      .agent-benefit-content {
        order: 2;
      }

      .benefit-image,
      .agent-benefit-image {
        order: 1;
      }
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px;
      padding: 30px;

      &:nth-child(even) {

        .benefit-content,
        .agent-benefit-content {
          order: 1;
        }

        .benefit-image,
        .agent-benefit-image {
          order: 2;
        }
      }
    }
  }

  .benefit-title,
  .agent-benefit-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
  }

  .benefit-description,
  .agent-benefit-description {
    color: var(--text-color-secondary);
    line-height: 1.6;
    font-size: 1.1rem;
  }

  .benefit-gif,
  .agent-benefit-gif {
    width: 100%;
    height: auto;
    border-radius: 8px;
  }

  // Buyer Benefits Section
  .buyer-benefits-section {
    background: #fff;
  }

  .crm-demo {
    margin-top: 40px;
  }

  .crm-gif {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  // Agent Benefits Section
  .agent-benefits-section {
    background: #fafafa;
    text-align: center;
  }

  // Pricing Section
  .pricing-section {
    background: #fafafa;
  }

  .pricing-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 40px;
    }
  }

  .pricing-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 20px;

    @media (max-width: 768px) {
      font-size: 1.875rem;
      text-align: center;
    }
  }

  .pricing-description {
    font-size: 1.1rem;
    color: var(--text-color-secondary);
    line-height: 1.6;
    margin-bottom: 30px;

    @media (max-width: 768px) {
      text-align: center;
    }
  }

  .pricing-gif {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  // Predatory Practices Section
  .predatory-practices-section {
    background: #fff;
  }

  .practices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
    gap: 40px;
    margin-top: 60px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 30px;
    }
  }

  .practice-card {
    background: #fafafa;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
  }

  .practice-image {
    margin-bottom: 20px;
  }

  .practice-gif {
    width: 100%;
    max-width: 150px;
    height: auto;
    border-radius: 8px;
  }

  .practice-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
  }

  .practice-description {
    color: var(--text-color-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
  }

  // Lenny Section
  .lenny-section {
    background: #fafafa;
  }

  .lenny-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 20px;
  }

  .lenny-description {
    font-size: 1.1rem;
    color: var(--text-color-secondary);
    line-height: 1.6;
    margin-bottom: 30px;
  }

  .lenny-gif {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .blog-button {
    background: #fff;
    border: 2px solid #1890ff;
    color: #1890ff;
    border-radius: 8px;
    font-weight: 600;
    padding: 10px 24px;
    height: auto;
    transition: all 0.3s ease;

    &:hover {
      background: #1890ff;
      color: #fff;
      transform: translateY(-2px);
    }
  }

  // Blog Section
  .blog-section {
    background: #fff;
  }

  .blog-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }


  .blog-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
    line-height: 1.3;
  }

  .blog-description {
    color: var(--text-color-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
    flex: 1;
  }

  .blog-gif {
    width: 100%;
    height: auto;
    border-radius: 8px;
  }

  .read-more-button {
    color: #1890ff;
    font-weight: 600;
    padding: 0;
    height: auto;

    &:hover {
      color: #722ed1;
    }
  }

  // Social Media Section
  .social-section {
    background: #fafafa;
    text-align: center;
  }

  .social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
    flex-wrap: wrap;
  }

  .social-link {
    display: inline-block;
    padding: 12px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  }

  .social-icon {
    width: 32px;
    height: 32px;
    display: block;
    font-size: 32px;
  }

  // Discord Section
  .discord-section {
    background: #fff;
  }

  .discord-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .discord-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 30px;
    line-height: 1.3;

    @media (max-width: 768px) {
      font-size: 1.75rem;
    }
  }

  .discord-benefits {
    list-style: none;
    padding: 0;
    margin: 0 0 30px 0;
  }

  .discord-benefit {
    padding: 12px 0;
    font-size: 1.1rem;
    color: var(--text-color-secondary);
    position: relative;
    padding-left: 30px;

    &:before {
      content: '✓';
      position: absolute;
      left: 0;
      color: #52c41a;
      font-weight: bold;
      font-size: 1.2rem;
    }
  }

  .discord-button {
    background: #5865f2;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    padding: 12px 32px;
    height: auto;
    box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);

    &:hover {
      background: #4752c4;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(88, 101, 242, 0.4);
    }
  }

  .discord-gif {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  // Final Message Section
  .final-message-section {
    background: linear-gradient(135deg, #1890ff, #722ed1);
    color: #fff;
    text-align: center;
  }

  .final-message {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 40px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    @media (max-width: 768px) {
      font-size: 2.25rem;
    }

    @media (max-width: 480px) {
      font-size: 1.875rem;
    }
  }

  .final-message-section .cta-button {
    background: #fff;
    color: #1890ff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

    &:hover {
      background: #f0f9ff;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    }
  }

  // Final CTA Section
  .final-cta-section {
    background: #fafafa;
    text-align: center;
  }

  .final-cta-image {
    margin: 40px 0;
  }

  .final-cta-gif {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  // Footer
  .footer {
    background: var(--text-color);
    color: #fff;
    text-align: center;
    padding: 30px 0;
  }

  .footer-text {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
  }

  // Animations
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {

    0%,
    100% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.05);
    }
  }

  // Scroll animations (can be enhanced with intersection observer)
  .step-card,
  .benefit-card,
  .agent-benefit-card,
  .practice-card,
  .blog-post {
    animation: fadeInUp 0.6s ease-out;
  }

  // Hover effects for GIFs
  .hero-gif,
  .demo-gif,
  .step-gif,
  .benefit-gif,
  .agent-benefit-gif,
  .crm-gif,
  .pricing-gif,
  .practice-gif,
  .lenny-gif,
  .blog-gif,
  .discord-gif,
  .final-cta-gif {
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
}