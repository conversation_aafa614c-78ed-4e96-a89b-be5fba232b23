import { Component, OnInit, SecurityContext } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { Router } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { DomSanitizer } from '@angular/platform-browser';

export interface FeatureItem {
  title: string;
  description: string;
  imageUrl: string;
  gifUrl?: string;
}

export interface HowItWorksStep {
  title: string;
  description: string;
  imageUrl: string;
  stepNumber: number;
}

export interface BenefitItem {
  title: string;
  description: string;
  imageUrl: string;
}

export interface BlogPost {
  title: string;
  description: string;
  imageUrl: string;
  link: string;
}

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.less'],
  imports: [SharedModule]
})
export class LandingPageComponent implements OnInit {

  // Security context for URL sanitization
  securityContext = SecurityContext;

  // Form properties
  websiteForm: FormGroup;
  isSubmitting = false;
  inputPlaceholder = 'Enter your website URL (e.g., https://yourwebsite.com) to generate a custom mortgage website...';

  // Hero section data
  heroTitle = 'Looking for a white-labeled mortgage toolkit to drive more homebuyer leads?';
  heroSubtitle = 'We help realtors attract more homebuyers with AI-powered mortgage and HELOC toolkits, —keeping clients in your ecosystem for future business.';
  heroImageUrl = '/assets/images/mortgage-tools-demo.gif';
  ctaButtonText = 'Request Your Invitation';
  ctaButtonUrl = 'https://lendsquid.typeform.com/to/ecK7w0OU';

  // How it works steps
  howItWorksSteps: HowItWorksStep[] = [
    {
      stepNumber: 1,
      title: 'Select Template',
      description: 'Browse hundreds of mortgage tool sites white labeled to your brand',
      imageUrl: '/assets/images/select-template.gif'
    },
    {
      stepNumber: 2,
      title: 'Connect CRM',
      description: 'Send leads directly to your CRM',
      imageUrl: '/assets/images/connect-crm.gif'
    },
    {
      stepNumber: 3,
      title: 'Share Tools with Homebuyers',
      description: 'Share your mortgage tools on social media, online, in blogs and drive inbound leads',
      imageUrl: '/assets/images/share-tools.gif'
    }
  ];

  // Buyer benefits features
  buyerBenefits: FeatureItem[] = [
    {
      title: 'AI Powered Home Valuation Tool',
      description: 'Help buyers understand what a home is really worth -instantly. You control the valuation experience, reinforcing your local expertise with AI power behind it.',
      imageUrl: '/assets/images/home-valuation.gif'
    },
    {
      title: 'AI Mortgage Calculators',
      description: 'Buyers can calculate real-time monthly payments with ease no guessing, no confusion. You become their trusted source for answers.',
      imageUrl: '/assets/images/mortgage-calculator.gif'
    },
    {
      title: 'Fast AI Powered Pre-Approvals',
      description: 'Guide your buyers to fast, reliable pre-approvals from lenders you trust - no lead-selling platforms, no spam. You protect their experience and keep the deal in your hands.',
      imageUrl: '/assets/images/pre-approval.gif'
    },
    {
      title: 'Secure Vault For Mortgage Docs',
      description: 'Your buyers get a private, secure place to upload and manage their mortgage documents. You stay in the loop, and the transaction stays on track -- with no third-party interference.',
      imageUrl: '/assets/images/secure-vault.gif'
    },
    {
      title: 'Mortgage AI Chatbot',
      description: 'Give your buyers 24/7 mortgage support with a personalized AI chatbot branded to you. It answers their questions, helps them get pre-approved, and positions you as the expert - all without any extra work.',
      imageUrl: '/assets/images/ai-chatbot.gif'
    }
  ];

  // Agent benefits
  agentBenefits: BenefitItem[] = [
    {
      title: 'More Leads',
      description: 'You attract buyers when they are just starting to shop for a home online.',
      imageUrl: '/assets/images/more-leads.gif'
    },
    {
      title: 'More Credibility',
      description: 'Buyers trust your professional opinion and view you an industry expert.',
      imageUrl: '/assets/images/more-credibility.gif'
    },
    {
      title: 'More Educated Buyers',
      description: 'Buyers will be knowledgeable about the mortgage process, what they can afford, their current home equity and more likely to retain you as their agent.',
      imageUrl: '/assets/images/educated-buyers.gif'
    }
  ];

  // Predatory practices
  predatoryPractices: FeatureItem[] = [
    {
      title: 'Lead Selling and Reselling',
      description: 'Realtors lose control of their clients when Fintech platforms intercept leads and sell them to competing agents or lenders.',
      imageUrl: '/assets/images/lead-selling.gif'
    },
    {
      title: 'Steering Borrowers To High Fee Loans',
      description: 'Many Fintech platforms operate as lead aggregators, sending borrowers to the lender who pays the most, not the one who offers the best deal.',
      imageUrl: '/assets/images/high-fee-loans.gif'
    },
    {
      title: 'Data Exploitation and Privacy',
      description: 'Fintech platforms collect massive amounts of personal data from homebuyers and use it to cross-sell other financial products.',
      imageUrl: '/assets/images/data-exploitation.gif'
    },
    {
      title: 'Undermining Realtor Client Relationships',
      description: 'Fintech lenders push their own "preferred agents" to homebuyers, redirecting clients away from their original realtors.',
      imageUrl: '/assets/images/undermining-relationships.gif'
    }
  ];

  // Blog posts
  blogPosts: BlogPost[] = [
    {
      title: 'Learn How Real Estate Agents Can Compete & Win in the Modern Marketplace',
      description: 'Uncover tips and strategies for realtors to offer more value to clients and sharpen their competitive edge',
      imageUrl: '/assets/images/blog-compete-win.gif',
      link: 'https://lennythesquid.com/2025/03/19/how-real-estate-agents-can-compete-and-win/'
    },
    {
      title: 'Real Estate in the Digital Age: How Technology Impacts the Home-Buying Process',
      description: 'Learn how technology is reshaping the home-buying journey—and how forward-thinking realtors can stay competitive, capture more clients, and thrive in a rapidly digitizing market.',
      imageUrl: '/assets/images/blog-digital-age.gif',
      link: 'https://lennythesquid.com/2025/03/17/how-technology-impacts-the-home-buying-process/'
    },
    {
      title: '8 Reasons Agents Need a Strong Digital Presence in the Modern Real Estate Market',
      description: 'Discussing the main advantages of realtors embracing the digital landscape',
      imageUrl: '/assets/images/blog-digital-presence.gif',
      link: 'https://lennythesquid.com/2025/03/26/8-reasons-agents-need-a-strong-digital-presence/'
    }
  ];

  // Social media links
  socialMediaLinks = [
    { platform: 'TikTok', url: 'https://www.tiktok.com/@lend_squid', icon: 'tik-tok', color: '#000000' },
    { platform: 'Instagram', url: 'https://www.instagram.com/LendSquid/', icon: 'instagram', color: '#E4405F' },
    { platform: 'YouTube', url: 'https://www.youtube.com/@LendSquid', icon: 'youtube', color: '#FF0000' },
    { platform: 'Twitter', url: 'https://x.com/Lend_Squid', icon: 'twitter', color: '#1DA1F2' },
    { platform: 'Facebook', url: 'https://www.facebook.com/people/LendSquid/61574605042938/', icon: 'facebook', color: '#1877F2' },
    { platform: 'LinkedIn', url: 'http://linkedin.com/company/lendsquid', icon: 'linkedin', color: '#0A66C2' }
  ];

  // Pricing
  startingPrice = '$1/month';
  pricingDescription = 'Get your mortgage tool site live for only $1/mo. Upgrades include custom domain, and homebuyer portal.';

  // Discord community
  discordUrl = 'https://discord.com/invite/BFCu4hGt';
  discordBenefits = [
    'How to stop losing clients to Fintech sharks',
    'Winning strategies for guiding homebuyers through the mortgage process & referrals',
    'Exclusive insights on the launch process & early access to LendSquid',
    'A support network of like-minded realtors taking control of homebuyer relationships'
  ];

  constructor(
    private fb: FormBuilder,
    private sanitizer: DomSanitizer,
    private router: Router
  ) {
    this.websiteForm = this.fb.group({
      websiteUrl: ['', [Validators.required, this.urlValidator]]
    });
  }

  ngOnInit(): void {
    // Component initialization logic
  }

  onCtaClick(): void {
    window.open(this.ctaButtonUrl, '_blank');
  }

  onBlogPostClick(link: string): void {
    window.open(link, '_blank');
  }

  onSocialMediaClick(url: string): void {
    window.open(url, '_blank');
  }

  onDiscordClick(): void {
    window.open(this.discordUrl, '_blank');
  }

  scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // Custom URL validator
  urlValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    const value = control.value.trim();

    // Check if it's a valid URL pattern
    if (!urlPattern.test(value)) {
      return { invalidUrl: true };
    }

    // Ensure it has a protocol
    if (!value.startsWith('http://') && !value.startsWith('https://')) {
      return { invalidUrl: true };
    }

    return null;
  }

  // Form submission handler
  onSubmitWebsite(): void {
    if (this.websiteForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      const unsafeUrl = this.websiteForm.get('websiteUrl')?.value;
      // Sanitize the URL
      const websiteUrl = this.sanitizer.sanitize(SecurityContext.URL, unsafeUrl) || '';

      console.log('websiteUrl', websiteUrl);

      setTimeout(() => {
        this.router.navigate(['/templates']);
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.websiteForm.controls).forEach(key => {
        this.websiteForm.get(key)?.markAsTouched();
      });
    }
  }
}
