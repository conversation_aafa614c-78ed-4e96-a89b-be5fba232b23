<!-- Landing Page Container -->
<div class="landing-page">

  <!-- Hero Section -->
  <section class="hero-section" id="hero">
    <div class="hero-container">
      
      <div class="hero-content">
        <h1 class="hero-title">{{ heroTitle }}</h1>

        <!-- AI Chatbot Style Input -->
        <div class="ai-input-container">
            <form [formGroup]="websiteForm" (ngSubmit)="onSubmitWebsite()" class="ai-form">
              <div class="input-field-container">
                <textarea nz-input formControlName="websiteUrl" [placeholder]="inputPlaceholder" class="ai-textarea"
                  [nzAutosize]="{ minRows: 4, maxRows: 4 }">
                </textarea>
              </div>
              <div class="validation-message"
                *ngIf="websiteForm.get('websiteUrl')?.invalid && websiteForm.get('websiteUrl')?.touched">
                <span *ngIf="websiteForm.get('websiteUrl')?.errors?.['required']">
                  Please enter your website URL
                </span>
                <span *ngIf="websiteForm.get('websiteUrl')?.errors?.['invalidUrl']">
                  Please enter a valid website URL (e.g., https://yourwebsite.com)
                </span>
              </div>

              <button nz-button nzType="primary" nzSize="large" class="cta-button" [nzLoading]="isSubmitting">
                Generate Page with AI
              </button>
            </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Value Proposition Section -->
  <section class="value-prop-section">
    <div class="container">
      <h2 class="section-title">Offer your own mortgage tools and drive active buyers to your CRM</h2>
      <p class="section-subtitle">{{ heroSubtitle }}</p>
      <div class="value-prop-image">
        <img src="assets/images/value-prop-demo.gif" alt="Value Proposition Demo" class="demo-gif">
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section class="how-it-works-section" id="how-it-works">
    <div class="container">
      <h2 class="section-title">How It Works</h2>
      <div class="steps-grid">
        <div class="step-card" *ngFor="let step of howItWorksSteps">
          <div class="step-image">
            <img [src]="step.imageUrl" [alt]="step.title" class="step-gif">
          </div>
          <div class="step-content">
            <h3 class="step-title">{{ step.title }}</h3>
            <div class="step-divider"></div>
            <p class="step-description">{{ step.description }}</p>
          </div>
        </div>
      </div>
      <div class="cta-section">
        <button nz-button nzType="primary" nzSize="large" class="cta-button" (click)="onCtaClick()">
          {{ ctaButtonText }}
        </button>
      </div>
    </div>
  </section>

  <!-- Buyer Benefits Section -->
  <section class="buyer-benefits-section" id="buyer-benefits">
    <div class="container">
      <h2 class="section-title">Here's how your buyers benefit</h2>
      <div class="benefits-grid">
        <div class="benefit-card" *ngFor="let benefit of buyerBenefits">
          <div class="benefit-content">
            <h3 class="benefit-title">{{ benefit.title }}</h3>
            <p class="benefit-description">{{ benefit.description }}</p>
          </div>
          <div class="benefit-image">
            <img [src]="benefit.imageUrl" [alt]="benefit.title" class="benefit-gif">
          </div>
        </div>
      </div>
      <div class="cta-section">
        <button nz-button nzType="primary" nzSize="large" class="cta-button" (click)="onCtaClick()">
          {{ ctaButtonText }}
        </button>
      </div>
    </div>
  </section>

  <!-- Agent Benefits Section -->
  <section class="agent-benefits-section" id="agent-benefits">
    <div class="container">
      <h2 class="section-title">Here's how real estate agents benefit</h2>
      <div class="agent-benefits-grid">
        <div class="agent-benefit-card" *ngFor="let benefit of agentBenefits">
          <div class="agent-benefit-content">
            <h3 class="agent-benefit-title">{{ benefit.title }}</h3>
            <p class="agent-benefit-description">{{ benefit.description }}</p>
          </div>
          <div class="agent-benefit-image">
            <img [src]="benefit.imageUrl" [alt]="benefit.title" class="agent-benefit-gif">
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section class="pricing-section" id="pricing">
    <div class="container">
      <div class="pricing-content">
        <div class="pricing-image">
          <img src="assets/images/pricing-demo.gif" alt="Pricing Demo" class="pricing-gif">
        </div>
        <div class="pricing-info">
          <h2 class="pricing-title">Plans start as low as {{ startingPrice }}</h2>
          <p class="pricing-description">{{ pricingDescription }}</p>
          <button nz-button nzType="primary" nzSize="large" class="cta-button" (click)="onCtaClick()">
            {{ ctaButtonText }}
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Predatory Practices Section -->
  <section class="predatory-practices-section" id="predatory-practices">
    <div class="container">
      <h2 class="section-title">Protect Homebuyers From Predatory Lending Practices</h2>
      <div class="practices-grid">
        <div class="practice-card" *ngFor="let practice of predatoryPractices">
          <div class="practice-image">
            <img [src]="practice.imageUrl" [alt]="practice.title" class="practice-gif">
          </div>
          <div class="practice-content">
            <h4 class="practice-title">{{ practice.title }}</h4>
            <p class="practice-description">{{ practice.description }}</p>
          </div>
        </div>
      </div>
      <div class="cta-section">
        <button nz-button nzType="primary" nzSize="large" class="cta-button" (click)="onCtaClick()">
          {{ ctaButtonText }}
        </button>
      </div>
    </div>
  </section>

  <!-- Lenny The Squid Section -->
  <section class="lenny-section" id="lenny">
    <div class="container">
      <div class="lenny-content">
        <div class="lenny-info">
          <h2 class="lenny-title">Meet Lenny The Squid</h2>
          <p class="lenny-description">
            <strong>Lenny isn't just a mascot - he's the fearless leader of the Squid movement, fighting against
              mortgage sharks and empowering real estate agents to take control of homebuyer relationships.</strong>
          </p>
          <button nz-button nzType="default" nzSize="large" class="blog-button"
            (click)="onBlogPostClick('https://lennythesquid.com/')">
            Visit Blog
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Blog Posts Section -->
  <section class="blog-section" id="blog">
    <div class="container">
      <div class="blog-posts">
        <div class="blog-post" *ngFor="let post of blogPosts">
          <div class="blog-content">
            <h3 class="blog-title">{{ post.title }}</h3>
            <p class="blog-description">{{ post.description }}</p>
            <button nz-button nzType="link" class="read-more-button" (click)="onBlogPostClick(post.link)">
              Read Article
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Social Media Section -->
  <section class="social-section" id="social">
    <div class="container">
      <h2 class="section-title">Follow Us On Social Media</h2>
      <p class="section-subtitle">Stay up to date on latest news, educational resources, and connect with other Squids
      </p>
      <div class="social-links">
        <a *ngFor="let social of socialMediaLinks" [href]="social.url" target="_blank" class="social-link"
          [title]="social.platform">
          <nz-icon [nzType]="social.icon" nzTheme="outline" class="social-icon" [style.color]="social.color" />
        </a>
      </div>
    </div>
  </section>

  <!-- Discord Community Section -->
  <section class="discord-section" id="discord">
    <div class="container">
      <div class="discord-content">
        <div class="discord-info">
          <h2 class="discord-title">Join our Discord community where realtors, mortgage brokers, and industry leaders
            discuss</h2>
          <ul class="discord-benefits">
            <li *ngFor="let benefit of discordBenefits" class="discord-benefit">{{ benefit }}</li>
          </ul>
          <button nz-button nzType="primary" nzSize="large" class="discord-button" (click)="onDiscordClick()">
            Join Discord Group
          </button>
        </div>
        <div class="discord-image">
          <img src="assets/images/discord-community.gif" alt="Discord Community" class="discord-gif">
        </div>
      </div>
    </div>
  </section>

  <!-- Final Message Section -->
  <section class="final-message-section">
    <div class="container">
      <h2 class="final-message">Lennything is possible with LendSquid!</h2>
      <div class="cta-section">
        <button nz-button nzType="primary" nzSize="large" class="cta-button" (click)="onCtaClick()">
          {{ ctaButtonText }}
        </button>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <p class="footer-text">LendSquid All Rights Reserved. Copyright 2025</p>
    </div>
  </footer>

</div>
