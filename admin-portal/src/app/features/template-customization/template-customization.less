@import '../../../styles/variables.less';

.template-customization-container {
  min-height: 100vh;
  background: var(--neutral-3);

  .container {
    margin: 0 auto;
    padding: 0 24px;
  }

  // Header Section
  .header-section {
    background: white;
    border-bottom: 1px solid @border-color-light;
    padding: 16px 0;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 24px;

      .header-left {

        .back-button {
          color: #666;

          &:hover {
            color: @info-color;
          }
        }
      }

      .header-tabs-actions {
        display: flex;
        align-items: center;
        gap: 32px;

        .header-tabs {
          display: flex;
          gap: 24px;
          align-items: center;

          .header-tab-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 18px;
            font-weight: 500;
            color: @secondary-text;
            cursor: pointer;
            padding: 6px 18px;
            border-radius: 6px;
            transition: background 0.2s, color 0.2s;

            &:hover {
              background: @primary-color-bg;
              color: @primary-color;
            }

            &.active {
              background: @primary-color-bg;
              color: @primary-color;
              font-weight: 600;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.08);
              border-bottom: 2px solid @primary-color;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 16px;

        .save-btn {
          background: @primary-color-bg;
          color: @primary-color;
          border: 1px solid @primary-color;
          font-weight: 500;
          transition: background 0.2s, color 0.2s;

          &:hover {
            background: @primary-color-bg;
            color: @primary-color;
            border-color: @primary-color;
          }
        }

        .publish-btn {
          background: @primary-color;
          color: @primary-color-bg;
          font-weight: 600;
          border: none;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);

          &:hover {
            background: @primary-color;
            color: @primary-color-bg;
          }
        }
      }
    }

    .template-info {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: @primary-text;
        margin-bottom: 4px;
      }

      .page-description {
        color: @secondary-text;
        margin-bottom: 0;
      }
    }
  }

  // Main Content
  .main-content {
    padding: 12px;

    .customization-tabs {
      background: @section-bg-white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      ::ng-deep .ant-tabs-nav {
        margin-bottom: 0;
        padding: 0 24px;
        background: @section-bg-light;
        border-radius: 8px 8px 0 0;

        .ant-tabs-tab {
          padding: 16px 24px;
          margin-right: 0;
          border: none;
          background: transparent;
          border-radius: 0;

          &:hover {
            color: @primary-color;
          }

          &.ant-tabs-tab-active {
            background: white;
            border-bottom: 2px solid @primary-color;
            color: @primary-color;
          }

          .ant-tabs-tab-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
          }
        }
      }

      ::ng-deep .ant-tabs-content-holder {
        padding: 24px;
        min-height: 600px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .template-customization-container {
    .header-section {
      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;

        .header-tabs-actions {
          flex-direction: column;
          gap: 12px;

          .header-tabs {
            gap: 12px;
            justify-content: center;
          }

          .header-actions {
            justify-content: center;
          }
        }
      }
    }

    .main-content {
      .customization-tabs {
        ::ng-deep .ant-tabs-nav {
          padding: 0 16px;

          .ant-tabs-tab {
            padding: 12px 16px;
          }
        }

        ::ng-deep .ant-tabs-content-holder {
          padding: 16px;
        }
      }
    }
  }
}