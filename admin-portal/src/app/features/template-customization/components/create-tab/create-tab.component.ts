import { AfterViewInit, Component, ElementRef, Input, OnD<PERSON>roy, Renderer2, ChangeDetectorRef, ChangeDetectionStrategy, OnChanges, SimpleChanges } from '@angular/core';
import { SharedModule } from '../../../../shared/shared.module';
import { Template, TemplatePreviewComponent } from 'lendsquid-templates';
import { SideEditorDrawerComponent } from './side-editor-drawer/side-editor-drawer.component';
import { NzSplitterModule } from 'ng-zorro-antd/splitter';
import { BehaviorSubject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

interface EditorChange {
  type: 'text' | 'image' | 'style' | 'theme';
  value: any;
}

@Component({
  selector: 'app-create-tab',
  imports: [SharedModule, TemplatePreviewComponent, SideEditorDrawerComponent, NzSplitterModule],
  templateUrl: './create-tab.component.html',
  styleUrl: './create-tab.component.less',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateTabComponent implements AfterViewInit, OnDestroy, OnChanges {
  @Input() selectedTemplate!: Template;

  selectedElementType: 'text' | 'image' | null = null;
  selectedTextValue: string = '';
  selectedImageSrc: string = '';
  selectedStyles: any = {};
  themeColors: { key: string; value: string }[] = [];

  private activeElement: HTMLElement | null = null;
  private listeners: (() => void)[] = [];
  private editorChange$ = new BehaviorSubject<EditorChange | null>(null);
  private subscriptions: Subscription[] = [];

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private changeDetector: ChangeDetectorRef
  ) {}

  ngAfterViewInit(): void {
    this.setupEventListeners();
    this.setupEditorChangeSubscription();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedTemplate'] && changes['selectedTemplate'].currentValue) {
      this.setThemeColorsFromTemplate();
    }
  }

  private setupEditorChangeSubscription(): void {
    this.subscriptions.push(
      this.editorChange$.pipe(
        debounceTime(200),
        distinctUntilChanged()
      ).subscribe(change => {
        if (change) {
          this.applyEditorChange(change);
        }
      })
    );
  }

  private applyEditorChange(change: EditorChange): void {
    switch (change.type) {
      case 'text':
        this.applyTextChange(change.value);
        break;
      case 'image':
        this.applyImageChange(change.value);
        break;
      case 'style':
        this.applyStyleChange(change.value);
        break;
      case 'theme':
        this.applyThemeChange(change.value);
        break;
    }
    this.changeDetector.markForCheck();
  }

  private setupEventListeners(): void {
    // Attach only a single click listener to the main-content-panel (template preview area)
    const previewPanel = this.el.nativeElement.querySelector('.main-content-panel');
    if (!previewPanel) return;

    this.listeners.push(
      this.renderer.listen(previewPanel, 'click', (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        const editable = target.closest('[data-displayname]');
        
        // Prevent default navigation for anchor elements
        if (target.tagName.toLowerCase() === 'a' || target.closest('a')) {
          event.preventDefault();
          event.stopPropagation();
        }
        
        if (editable) {
          this.setSelectedElement(editable as HTMLElement);
          this.changeDetector.detectChanges();
        }
      })
    );
  }

  private getElementText(element: HTMLElement): string {
    const clone = element.cloneNode(true) as HTMLElement;
    const button = clone.querySelector('.edit-btn');
    if (button) {
      clone.removeChild(button);
    }
    return clone.innerText.trim();
  }

  private setSelectedElement(element: HTMLElement): void {
    this.activeElement = element;
    const isImage = element.tagName.toLowerCase() === 'img' || element.style.backgroundImage !== '';
    this.selectedElementType = isImage ? 'image' : 'text';
    this.selectedTextValue = isImage ? '' : this.getElementText(element);
    this.selectedImageSrc = isImage ? this.extractImageSrc(element) : '';
    const computedStyles = window.getComputedStyle(element);
    this.selectedStyles = {
      fontSize: computedStyles.fontSize.replace('px', ''),
      fontWeight: computedStyles.fontWeight,
      fontFamily: computedStyles.fontFamily,
      color: computedStyles.color,
      textAlign: computedStyles.textAlign,
      objectFit: isImage ? computedStyles.objectFit : undefined
    };
  }

  private extractImageSrc(element: HTMLElement): string {
    // Check if it's an img element first
    if (element.tagName.toLowerCase() === 'img') {
      return (element as HTMLImageElement).src;
    }
    
    // Check for background image
    const backgroundImage = element.style.backgroundImage;
    if (backgroundImage && backgroundImage !== 'none') {
      // Extract URL from url() function
      const urlMatch = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
      return urlMatch ? urlMatch[1] : '';
    }
    
    // Check computed styles for background image
    const computedStyles = window.getComputedStyle(element);
    const computedBackgroundImage = computedStyles.backgroundImage;
    if (computedBackgroundImage && computedBackgroundImage !== 'none') {
      const urlMatch = computedBackgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
      return urlMatch ? urlMatch[1] : '';
    }
    
    return '';
  }

  // private clearSelectedElement(): void {
  //   this.selectedElementType = null;
  //   this.selectedTextValue = '';
  //   this.selectedImageSrc = '';
  //   this.selectedStyles = {};
  // }

  private setThemeColorsFromTemplate(): void {
    const theme = this.selectedTemplate?.theme || {};
    this.themeColors = [
      { key: 'Primary Color', value: theme.primaryColor },
      { key: 'Secondary Color', value: theme.secondaryColor },
      { key: 'Background Color', value: theme.backgroundColor },
      { key: 'Secondary Background Color', value: theme.secondaryBackgroundColor },
      { key: 'Primary Text Color', value: theme.primaryTextColor },
      { key: 'Secondary Text Color', value: theme.secondaryTextColor },
      { key: 'Tertiary Text Color', value: theme.tertiaryTextColor },
      { key: 'Font Family', value: theme.fontFamily }
    ];
  }

  // Optimized editor change handlers with debouncing
  onTextChange(value: string): void {
    this.editorChange$.next({ type: 'text', value });
  }

  onImageChange(value: string): void {
    this.editorChange$.next({ type: 'image', value });
  }

  onStyleChange(styles: any): void {
    this.editorChange$.next({ type: 'style', value: styles });
  }

  onThemeChange(themeColors: any): void {
    this.editorChange$.next({ type: 'theme', value: themeColors });
  }

  // DOM update methods
  private applyTextChange(value: string): void {
    if (this.activeElement && this.selectedElementType === 'text') {
      // Update DOM
      const textNodes = Array.from(this.activeElement.childNodes).filter(
        (node): node is Text =>
          node.nodeType === Node.TEXT_NODE
      );
      if (textNodes.length > 0) {
        const mainTextNode = textNodes[textNodes.length - 1];
        const originalText = mainTextNode.textContent || '';
        const leadingSpace = originalText.match(/^\s*/)?.[0] || '';
        this.renderer.setValue(mainTextNode, `${leadingSpace}${value}`);
      }

      // Update selectedTemplate
      this.selectedTextValue = value;
      this.updateTemplateContent();
    }
  }

  private applyImageChange(value: string): void {
    if (this.activeElement && this.selectedElementType === 'image') {
      // Update DOM based on element type
      if (this.activeElement.tagName.toLowerCase() === 'img') {
        this.renderer.setAttribute(this.activeElement, 'src', value);
      } else {
        // For background images, wrap the URL in url() function
        this.renderer.setStyle(this.activeElement, 'backgroundImage', `url("${value}")`);
      }
      
      // Update selectedTemplate
      this.selectedImageSrc = value;
      this.updateTemplateContent();
    }
  }

  private applyStyleChange(styles: any): void {
    if (this.activeElement) {
      // Update DOM
      Object.entries(styles).forEach(([key, value]) => {
        if (value !== undefined) {
          this.renderer.setStyle(this.activeElement, key, value);
        }
      });

      // Update selectedTemplate
      this.selectedStyles = { ...this.selectedStyles, ...styles };
      this.updateTemplateContent();
    }
  }

  private applyThemeChange(themeColors: { key: string; value: string }[]): void {
    // Map themeColors back to ThemeConfiguration keys
    if (!this.selectedTemplate || !this.selectedTemplate.theme) return;
    
    // Create a new theme object to trigger change detection
    const updatedTheme = {
      ...this.selectedTemplate.theme,
      primaryColor: themeColors.find(color => color.key === 'Primary Color')?.value || '',
      secondaryColor: themeColors.find(color => color.key === 'Secondary Color')?.value || '',
      backgroundColor: themeColors.find(color => color.key === 'Background Color')?.value || '',
      secondaryBackgroundColor: themeColors.find(color => color.key === 'Secondary Background Color')?.value || '',
      primaryTextColor: themeColors.find(color => color.key === 'Primary Text Color')?.value || '',
      secondaryTextColor: themeColors.find(color => color.key === 'Secondary Text Color')?.value || '',
      tertiaryTextColor: themeColors.find(color => color.key === 'Tertiary Text Color')?.value || '',
      fontFamily: themeColors.find(color => color.key === 'Font Family')?.value || ''
    };
    
    // Create a new template object to trigger change detection
    this.selectedTemplate = {
      ...this.selectedTemplate,
      theme: updatedTheme
    };
    
    // Update CSS custom properties directly in the DOM
    this.updateThemeCSSVariables(updatedTheme);
    
    // Force change detection
    this.changeDetector.markForCheck();
  }

  private updateThemeCSSVariables(theme: any): void {
    // Find the template root element (the one with class 'ls-template')
    const templateElement = this.el.nativeElement.querySelector('.ls-template');
    if (!templateElement) return;
    
    // Update CSS custom properties
    this.renderer.setStyle(templateElement, '--ls-primary-color', theme.primaryColor);
    this.renderer.setStyle(templateElement, '--ls-secondary-color', theme.secondaryColor);
    this.renderer.setStyle(templateElement, '--ls-background-color', theme.backgroundColor);
    this.renderer.setStyle(templateElement, '--ls-secondary-background-color', theme.secondaryBackgroundColor);
    this.renderer.setStyle(templateElement, '--ls-primary-text-color', theme.primaryTextColor);
    this.renderer.setStyle(templateElement, '--ls-secondary-text-color', theme.secondaryTextColor);
    this.renderer.setStyle(templateElement, '--ls-tertiary-text-color', theme.tertiaryTextColor);
    this.renderer.setStyle(templateElement, '--ls-font-family', theme.fontFamily);
  }

  private updateTemplateContent(): void {
    if (!this.selectedTemplate || !this.activeElement) return;

    const displayName = this.activeElement.getAttribute('data-displayname');
    if (!displayName) return;

    // Initialize customizations if they don't exist
    if (!(this.selectedTemplate as any).customizations) {
      (this.selectedTemplate as any).customizations = {
        content: {},
        styles: {}
      };
    }

    // Update content based on element type
    if (this.selectedElementType === 'text') {
      (this.selectedTemplate as any).customizations.content[displayName] = this.selectedTextValue;
    } else if (this.selectedElementType === 'image') {
      (this.selectedTemplate as any).customizations.content[displayName] = this.selectedImageSrc;
    }

    // Update styles
    (this.selectedTemplate as any).customizations.styles[displayName] = {
      ...(this.selectedTemplate as any).customizations.styles[displayName],
      ...this.selectedStyles
    };
  }

  /**
   * Get the updated template with all customizations
   */
  getUpdatedTemplate(): Template {
    return this.selectedTemplate;
  }

  /**
   * Get all customizations made to the template
   */
  getCustomizations(): { content: Record<string, any>; styles: Record<string, any> } {
    return (this.selectedTemplate as any).customizations || { content: {}, styles: {} };
  }

  ngOnDestroy(): void {
    this.listeners.forEach(l => l());
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.editorChange$.complete();
  }
} 