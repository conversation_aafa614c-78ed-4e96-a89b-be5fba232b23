import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSliderModule } from 'ng-zorro-antd/slider';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzUploadModule, NzUploadChangeParam } from 'ng-zorro-antd/upload';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzColorPickerModule, NzColor } from 'ng-zorro-antd/color-picker';

export interface TextImageStyles {
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
  color?: string;
  textAlign?: string;
  objectFit?: string;
}

const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });

@Component({
  selector: 'app-side-editor-drawer',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzInputModule,
    NzButtonModule,
    NzDividerModule,
    NzCollapseModule,
    NzSelectModule,
    NzSliderModule,
    NzRadioModule,
    NzUploadModule,
    NzIconModule,
    NzFormModule,
    NzTabsModule,
    NzColorPickerModule
  ],
  templateUrl: './side-editor-drawer.component.html',
  styleUrls: ['./side-editor-drawer.component.less']
})
export class SideEditorDrawerComponent {
  @Input() elementType: 'text' | 'image' = 'text';
  @Input() textValue: string = '';
  @Input() imageSrc: string = '';
  @Input() styles: TextImageStyles = {};
  @Input() themeColors: { key: string; value: string }[] = [];
  
  @Input() hasSelection: boolean = false;
  @Output() textChange = new EventEmitter<string>();
  @Output() imageChange = new EventEmitter<string>();
  @Output() styleChange = new EventEmitter<TextImageStyles>();
  @Output() themeChange = new EventEmitter<{ key: string; value: string }[]>();

  fontOptions = [
    { label: 'Primary Font', value: 'Primary Font', style: 'font-family: inherit;' },
    { label: 'Arial', value: 'Arial', style: 'font-family: Arial, sans-serif;' },
    { label: 'Roboto', value: 'Roboto', style: 'font-family: Roboto, Arial, sans-serif;' },
    { label: 'Georgia', value: 'Georgia', style: 'font-family: Georgia, serif;' },
    { label: 'Montserrat', value: 'Montserrat', style: 'font-family: Montserrat, sans-serif;' },
    { label: 'Lato', value: 'Lato', style: 'font-family: Lato, sans-serif;' }
  ];

  updateStyle(key: keyof TextImageStyles, value: string | NzColor) {
    let colorValue = value;
    if (typeof value !== 'string' && value && typeof value.toHexString === 'function') {
      colorValue = value.toHexString();
    }
    this.styles = { ...this.styles, [key]: colorValue as string };
    this.styleChange.emit(this.styles);
  }

  handleImageChange(event: NzUploadChangeParam) {
    const file = event.file;
    if (file && file.originFileObj) {
      getBase64(file.originFileObj).then(base64 => {
        this.imageChange.emit(base64 as string);
      });
    }
  }

  setThemeColor(key: string, value: string | NzColor) {
    let colorValue = value;
    if (typeof value !== 'string' && value && typeof value.toHexString === 'function') {
      colorValue = value.toHexString();
    }
    
    // Find and update existing color or add new one
    const existingIndex = this.themeColors.findIndex(color => color.key === key);
    if (existingIndex !== -1) {
      // Update existing color
      this.themeColors = this.themeColors.map((color, index) => 
        index === existingIndex ? { ...color, value: colorValue as string } : color
      );
    } else {
      // Add new color
      this.themeColors = [...this.themeColors, { key, value: colorValue as string }];
    }
    
    this.themeChange.emit(this.themeColors);
  }
} 