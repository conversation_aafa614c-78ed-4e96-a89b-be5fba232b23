#template-editor-panel {
  &.side-editor-panel {
    background: #23232b;
    color: #fff;
    border-left: 1px solid #23232b;
    border-radius: 0 12px 12px 0;
    box-shadow: -2px 0 12px rgba(0, 0, 0, 0.12);
    height: calc(100vh - 140px - (12px * 2));
    overflow-y: auto;
  }

  .section-label {
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .ant-tabs {
    color: #fff;
  }

  nz-tabset {
    .ant-tabs-nav {
      background: #23232b;
      border-radius: 8px 8px 0 0;

      .ant-tabs-tab {
        color: #bfbfbf;
        font-weight: 500;

        &.ant-tabs-tab-active {
          color: #fff;
          background: #18181f;
          border-radius: 8px 8px 0 0;
        }
      }
    }
  }

  nz-form-item,
  .editor-controls,
  .image-controls {
    background: #18181f;
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 12px;
  }

  nz-select,
  nz-select .ant-select-selector {
    background: #23232b !important;
    color: #fff !important;
    border-radius: 6px !important;
    border: 1px solid #444 !important;
  }

  nz-option span {
    color: #fff;
    font-size: 15px;
  }

  nz-color-picker {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    background: #18181f;
  }

  .char-counter {
    color: #bfbfbf;
  }

  .default-context {
    color: #bfbfbf;
    background: #18181f;
    border-radius: 8px;
    padding: 32px 12px;

    .default-context-info {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
  }

  .text-editor-section,
  .image-editor-section {
    background: #18181f;
    border-radius: 8px;
    padding: 16px 12px;
    margin-bottom: 16px;
  }

  .theme-editor-section {
    background: #18181f;
    border-radius: 8px;
    padding: 16px 12px;
  }

  .theme-color-row {
    background: #23232b;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: space-between;
  }

  .color-label {
    color: #fff;
    min-width: 120px;
    font-weight: 500;
  }

  .color-value {
    color: #bfbfbf;
    font-size: 12px;
  }

  nz-slider .ant-slider-track {
    background: #7c3aed !important;
  }

  nz-slider .ant-slider-handle {
    border-color: #7c3aed !important;
  }

  nz-radio-group label[nz-radio] {
    background: #23232b;
    border-radius: 4px;
    margin-right: 4px;
    color: #fff;
    border: 1px solid #444;
    padding: 2px 8px;
  }

  nz-radio-group label[nz-radio].ant-radio-wrapper-checked {
    background: #7c3aed;
    color: #fff;
    border-color: #7c3aed;
  }

  textarea[nz-input] {
    background: #23232b;
    color: #fff;
    border-radius: 6px;
    border: 2px solid #665dc0 !important;
    font-size: 16px;
    padding: 10px;
  }

  ::-webkit-scrollbar {
    width: 8px;
    background: #23232b;
  }

  ::-webkit-scrollbar-thumb {
    background: #18181f;
    border-radius: 8px;
  }

  .drawer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  .image-preview {
    width: 100%;
    max-width: 220px;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .text-format-toolbar {
    display: flex;
    gap: 8px;
    background: #23232b;
    border-radius: 6px;
    padding: 6px 8px;
    margin-bottom: 10px;
    justify-content: flex-start;
  }

  .text-format-toolbar button[nz-button] {
    background: #18181f;
    color: #fff;
    border: 1px solid #444;
    box-shadow: none;
    transition: background 0.2s, border 0.2s;
  }

  .text-format-toolbar button[nz-button]:hover,
  .text-format-toolbar button[nz-button]:focus {
    background: #7c3aed;
    color: #fff;
    border-color: #7c3aed;
  }

  .text-format-toolbar button[nz-button].active {
    background: #7c3aed;
    color: #fff;
    border-color: #7c3aed;
  }

}