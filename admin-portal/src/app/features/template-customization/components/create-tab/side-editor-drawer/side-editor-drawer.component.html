<div class="side-editor-panel" id="template-editor-panel">
  <nz-tabset>
    <nz-tab nzTitle="Content Editor">
      <div class="default-context" *ngIf="!hasSelection; else contentEditor">
        <div class="default-context-info">
          <span nz-icon nzType="info-circle"></span>
          <span>Select an element to edit</span>
        </div>
        <p>Click on any text or image in the template to customize its content and style.</p>
      </div>
      <ng-template #contentEditor>
        <ng-container *ngIf="elementType === 'text'; else imageEditor">
          <div class="text-editor-section">
            <textarea nz-input [(ngModel)]="textValue" (ngModelChange)="textChange.emit($event)" rows="4"
              maxlength="200" placeholder="Edit text..."></textarea>
            <div class="char-counter">{{ textValue.length }} / 200</div>
          </div>
        </ng-container>
        <ng-template #imageEditor>
          <div class="image-editor-section">
            <img [src]="imageSrc" alt="Image preview" class="image-preview" />
            <nz-upload [nzShowUploadList]="false" (nzChange)="handleImageChange($event)">
              <button nz-button><span nz-icon nzType="upload"></span> Choose Image</button>
            </nz-upload>
          </div>
        </ng-template>
      </ng-template>
    </nz-tab>
    <nz-tab nzTitle="Theme Editor">
      <div class="theme-editor-section">
        <div *ngFor="let color of themeColors; let i = index" class="theme-color-row">
          <span class="color-label">{{ color.key }}</span>
          <nz-color-picker [nzValue]="color.value"
            (nzOnChange)="setThemeColor(color.key, $event.color)">
          </nz-color-picker>
        </div>
      </div>
    </nz-tab>
  </nz-tabset>
</div>