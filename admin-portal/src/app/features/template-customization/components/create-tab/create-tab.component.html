<nz-splitter [nzLayout]="'horizontal'">
  <nz-splitter-panel nzDefaultSize="80%" nzMin="50%" nzMax="90%">
    <div class="main-content-panel">
      <ng-container *ngIf="selectedTemplate; else loading">
        <ls-template-preview [template]="selectedTemplate"></ls-template-preview>
      </ng-container>
      <ng-template #loading>
        <div class="loading-state">
          <nz-spin nzTip="Loading template..."></nz-spin>
        </div>
      </ng-template>
    </div>
  </nz-splitter-panel>
  <nz-splitter-panel nzDefaultSize="20%" nzMin="10%" nzMax="30%">
    <app-side-editor-drawer [elementType]="selectedElementType || 'text'" [textValue]="selectedTextValue"
      [imageSrc]="selectedImageSrc" [styles]="selectedStyles" [themeColors]="themeColors"
      [hasSelection]="!!selectedElementType" (textChange)="onTextChange($event)" (imageChange)="onImageChange($event)"
      (styleChange)="onStyleChange($event)" (themeChange)="onThemeChange($event)">
    </app-side-editor-drawer>
  </nz-splitter-panel>
</nz-splitter>