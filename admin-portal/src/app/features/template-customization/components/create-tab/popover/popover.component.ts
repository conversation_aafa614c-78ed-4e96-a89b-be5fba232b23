import { Component, Inject } from '@angular/core';
import { SharedModule } from '../../../../../shared/shared.module';
import { FormsModule } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-popover',
  templateUrl: './popover.component.html',
  styleUrls: ['./popover.component.less'],
  imports: [SharedModule, FormsModule],
  standalone: true
})
export class PopoverComponent {
  value: string = '';
  displayName: string = '';
  
  charCount: number = 0;
  
  constructor(
    private modalRef: NzModalRef,
    @Inject(NZ_MODAL_DATA) public data: { value: string, displayName: string }
  ) {
    this.value = this.data.value;
    this.displayName = this.data.displayName;
  }

  ngOnInit() {
    this.charCount = this.value.length;
  }

  updateCharCount() {
    this.charCount = this.value.length;
  }

  save() {
    this.modalRef.close(this.value);
  }

  close() {
    this.modalRef.close();
  }
} 