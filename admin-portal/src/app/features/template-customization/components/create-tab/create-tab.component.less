.tab-content {
  padding: 24px 0;
}

.main-content-panel {
  height: calc(100vh - 140px - (12px * 2));
  overflow-y: auto;
}

:host ::ng-deep {
  [data-displayname] {
    position: relative;
    cursor: pointer;

    &:hover {
      outline: 2px solid var(--ant-primary-color);
      outline-offset: 2px;
      border-radius: 4px;
    }

    &:hover::after {
      content: 'Edit';
      position: absolute;
      top: -15px;
      right: -15px;
      background: #fff;
      color: #333;
      border-radius: 4px;
      padding: 2px 8px;
      font-size: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      z-index: 10;
      cursor: pointer;
      border: 1px solid #ccc;
      font-family: inherit;
      font-weight: 500;
    }
  }
}

// Responsive design
@media (max-width: 768px) {}

.default-context {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #888;
}

nz-tabset {
  width: 100%;
}

@media (max-width: 900px) {
  .splitter {
    width: 100%;
    height: 6px;
    cursor: row-resize;
  }
}