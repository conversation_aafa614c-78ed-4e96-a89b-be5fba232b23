.tab-content {
  padding: 24px 0;

  .domain-panel {
    h2 {
      font-size: 28px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    >p {
      font-size: 16px;
      color: #595959;
      margin-bottom: 32px;
    }
  }

  .payment-plans-section {
    margin-bottom: 40px;

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 24px;
    }

    .payment-plans {
      gap: 40px;
      display: flex;
      justify-content: center;

      .plan-card {
        position: relative;
        border: 2px solid #f0f0f0;
        border-radius: 12px;
        padding: 24px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 320px;

        &:hover {
          border-color: #1890ff;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        &.selected {
          border-color: #1890ff;
          background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
          box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
        }

        &.popular {
          border-color: #52c41a;
          background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);

          .popular-badge {
            position: absolute;
            top: -12px;
            right: 24px;
            background: #52c41a;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
          }
        }

        &.coming-soon {
          opacity: 0.6;
          pointer-events: none;
          filter: grayscale(30%);
          position: relative;

          .coming-soon-badge {
            position: absolute;
            top: -12px;
            left: 24px;
            background: #faad14;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(250, 173, 20, 0.15);
          }
        }

        .plan-header {
          text-align: center;
          margin-bottom: 24px;

          h4 {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
          }

          .plan-price {
            margin-bottom: 8px;

            .currency {
              font-size: 16px;
              color: #595959;
              vertical-align: top;
            }

            .amount {
              font-size: 32px;
              font-weight: 700;
              color: #1890ff;
            }

            .interval {
              font-size: 14px;
              color: #8c8c8c;
            }
          }

          .plan-description {
            color: #595959;
            font-size: 14px;
            line-height: 1.4;
          }
        }

        .plan-features {
          margin-bottom: 24px;

          ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              color: #595959;
              font-size: 14px;

              .feature-check {
                color: #52c41a;
                margin-right: 8px;
                font-size: 16px;
              }
            }
          }
        }

        .plan-action {
          button {
            border-radius: 8px;
            font-weight: 600;
            height: 40px;
          }
        }
      }
    }
  }

  .domain-configuration {
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 24px;
    }

    .domain-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
      margin-bottom: 32px;

      .domain-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        &.premium {
          opacity: 0.6;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            pointer-events: none;
          }
        }

        ::ng-deep .ant-card-head {
          background-color: #fafafa;
          border-bottom: 1px solid #f0f0f0;
          border-radius: 8px 8px 0 0;

          .ant-card-head-title {
            font-weight: 600;
            color: #262626;
          }
        }

        .domain-content {
          p {
            color: #595959;
            margin-bottom: 16px;
            line-height: 1.5;
          }

          .domain-input {
            margin-bottom: 12px;
          }

          .domain-example {
            color: #1890ff;
            font-size: 14px;
            margin-bottom: 8px;
          }

          .upgrade-notice {
            display: flex;
            align-items: center;
            color: #faad14;
            font-size: 12px;
            font-weight: 500;

            nz-icon {
              margin-right: 4px;
            }
          }
        }
      }
    }

    .payment-action {
      text-align: center;

      button {
        border-radius: 8px;
        font-weight: 600;
        height: 48px;
        padding: 0 32px;
        font-size: 16px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .tab-content {
    padding: 16px 0;

    .domain-panel {
      h2 {
        font-size: 24px;
      }
    }

    .payment-plans-section {
      .payment-plans {
        grid-template-columns: 1fr;
      }
    }

    .domain-configuration {
      .domain-options {
        grid-template-columns: 1fr;
      }
    }
  }
}