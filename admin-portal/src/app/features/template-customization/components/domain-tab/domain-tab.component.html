<div class="tab-content">
  <div class="domain-panel">
    <p>Select a plan and configure your website domain.</p>

    <!-- Payment Plans -->
    <div class="payment-plans-section">
      <div class="payment-plans">
        <div *ngFor="let plan of paymentPlans" class="plan-card"
          [class.selected]="selectedPlan?.id === plan.id"
          [class.popular]="plan.isPopular"
          [class.coming-soon]="plan.comingSoon"
          (click)="!plan.comingSoon && selectPlan(plan)">

          <div class="plan-header">
            <h4>{{ plan.name }}</h4>
            <div class="plan-price">
              <span class="currency">$</span>
              <span class="amount">{{ plan.price }}</span>
              <span class="interval" *ngIf="plan.price > 0">/{{ plan.interval }}</span>
            </div>
            <p class="plan-description">{{ plan.description }}</p>
          </div>

          <div class="plan-features">
            <ul>
              <li *ngFor="let feature of plan.features">
                <span nz-icon nzType="check" class="feature-check"></span>
                {{ feature }}
              </li>
            </ul>
          </div>

          <div class="plan-action">
            <button nz-button
              [nzType]="selectedPlan?.id === plan.id ? 'primary' : 'default'"
              nzBlock
              [disabled]="plan.comingSoon">
              {{ plan.comingSoon ? 'Coming Soon' : (selectedPlan?.id === plan.id ? 'Selected' : 'Select Plan') }}
            </button>
          </div>

          <div *ngIf="plan.isPopular" class="popular-badge">
            Most Popular
          </div>
          <div *ngIf="plan.comingSoon" class="coming-soon-badge">
            Coming Soon
          </div>
        </div>
      </div>
    </div>

    <!-- Domain Configuration -->
    <div class="domain-configuration" *ngIf="selectedPlan">
      <h3>Configure Your Domain</h3>

      <div class="domain-options">
        <!-- <nz-card nzTitle="Free Subdomain" class="domain-card">
          <div class="domain-content">
            <p>Get a free subdomain to get started quickly.</p>
            <div class="domain-input">
              <nz-input-group nzAddOnAfter=".lendsquid.com">
                <input nz-input [(ngModel)]="subdomain" placeholder="your-business"
                  [disabled]="selectedPlan.price > 0">
              </nz-input-group>
            </div>
            <div class="domain-example">
              <strong>{{ subdomain || 'your-business' }}.lendsquid.com</strong>
            </div>
          </div>
        </nz-card> -->

        <nz-card nzTitle="Custom Domain" class="domain-card" [class.premium]="selectedPlan.price === 0">
          <div class="domain-content">
            <p>Use your own custom domain name.</p>
            <div class="domain-input">
              <input nz-input [(ngModel)]="customDomain" placeholder="www.yourbusiness.com"
                [disabled]="selectedPlan.price === 0">
            </div>
            <div class="domain-example">
              <strong>{{ customDomain || 'www.yourbusiness.com' }}</strong>
            </div>
            <div *ngIf="selectedPlan.price === 0" class="upgrade-notice">
              <span nz-icon nzType="crown"></span>
              Upgrade to use custom domain
            </div>
          </div>
        </nz-card>
      </div>

      <!-- Payment Action -->
      <div class="payment-action">
        <button nz-button nzType="primary" nzSize="large" (click)="proceedWithPayment()">
          <span nz-icon nzType="credit-card" *ngIf="selectedPlan.price > 0"></span>
          <span nz-icon nzType="check" *ngIf="selectedPlan.price === 0"></span>
          {{ selectedPlan.price > 0 ? 'Proceed to Payment' : 'Activate Free Plan' }}
        </button>
      </div>
    </div>
  </div>
</div> 