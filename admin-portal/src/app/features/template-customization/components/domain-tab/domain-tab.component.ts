import { Component, Input, Output, EventEmitter } from '@angular/core';
import { SharedModule } from '../../../../shared/shared.module';
import { PaymentPlan } from '../../../../core/services/payment.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-domain-tab',
  imports: [SharedModule],
  templateUrl: './domain-tab.component.html',
  styleUrl: './domain-tab.component.less',
  standalone: true
})
export class DomainTabComponent {
  @Input() paymentPlans: PaymentPlan[] = [];
  @Input() selectedPlan: PaymentPlan | null = null;
  @Output() planSelected = new EventEmitter<PaymentPlan>();
  @Output() paymentProceeded = new EventEmitter<void>();

  subdomain = '';
  customDomain = '';

  constructor(
    private message: NzMessageService
  ) {}

  selectPlan(plan: PaymentPlan): void {
    this.planSelected.emit(plan);
  }

  proceedWithPayment(): void {
    if (!this.selectedPlan) {
      this.message.warning('Please select a plan first');
      return;
    }

    if (this.selectedPlan.price > 0) {
      // Validate domain configuration for paid plans
      if (!this.customDomain.trim()) {
        this.message.warning('Please enter a custom domain for paid plans');
        return;
      }
    } else {
      // Validate subdomain for free plans
      if (!this.subdomain.trim()) {
        this.message.warning('Please enter a subdomain');
        return;
      }
    }

    this.paymentProceeded.emit();
  }

  getDomainExample(): string {
    if (this.selectedPlan?.price && this.selectedPlan.price > 0) {
      return this.customDomain || 'www.yourbusiness.com';
    } else {
      return `${this.subdomain || 'your-business'}.lendsquid.com`;
    }
  }
} 