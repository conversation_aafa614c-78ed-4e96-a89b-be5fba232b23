<div class="tab-content">
  <div class="connect-panel">
    <h2>Connect Your Services</h2>
    <p>Integrate with Google Sheets, Zapier, and other services to automate your workflow.</p>

    <!-- Google Sheets Integration -->
    <div class="integration-section">
      <h3>
        <span nz-icon nzType="file-excel"></span>
        Google Sheets Integration
      </h3>
      <p>Automatically send form submissions and leads to your Google Sheets.</p>

      <div class="add-integration">
        <nz-input-group nzAddOnAfter="addButton">
          <input nz-input [(ngModel)]="newSheetsUrl" placeholder="Paste your Google Sheets URL here">
          <ng-template #addButton>
            <button nz-button nzType="primary" (click)="addGoogleSheets()"
              [disabled]="!newSheetsUrl.trim()">
              <span nz-icon nzType="plus"></span>
              Add
            </button>
          </ng-template>
        </nz-input-group>
      </div>

      <div class="integrations-list" *ngIf="googleSheetsIntegrations.length > 0">
        <nz-card *ngFor="let integration of googleSheetsIntegrations" class="integration-item">
          <div class="integration-header">
            <div class="integration-info">
              <h4>{{ integration.name }}</h4>
              <p>{{ integration.worksheetName }} worksheet</p>
              <a [href]="integration.spreadsheetUrl" target="_blank" class="sheets-link">
                <span nz-icon nzType="external-link"></span>
                Open in Google Sheets
              </a>
            </div>
            <div class="integration-status">
              <span class="status-indicator" [class.connected]="integration.isActive"
                [class.disconnected]="!integration.isActive">
              </span>
              {{ integration.isActive ? 'Connected' : 'Disconnected' }}
            </div>
          </div>
          <div class="integration-actions">
            <button nz-button nzType="default" nzSize="small" (click)="testGoogleSheets(integration)">
              <span nz-icon nzType="experiment"></span>
              Test
            </button>
            <button nz-button nzType="default" nzSize="small" nzDanger
              (click)="removeGoogleSheets(integration.id)">
              <span nz-icon nzType="delete"></span>
              Remove
            </button>
          </div>
        </nz-card>
      </div>
    </div>

    <!-- Zapier Webhooks -->
    <div class="integration-section">
      <h3>
        <span nz-icon nzType="api"></span>
        Zapier Webhooks
      </h3>
      <p>Connect to Zapier webhooks to trigger custom automations.</p>

      <div class="add-integration">
        <nz-input-group nzAddOnAfter="addWebhookButton">
          <input nz-input [(ngModel)]="newWebhookUrl" placeholder="Paste your Zapier webhook URL here">
          <ng-template #addWebhookButton>
            <button nz-button nzType="primary" (click)="addWebhook()" [disabled]="!newWebhookUrl.trim()">
              <span nz-icon nzType="plus"></span>
              Add
            </button>
          </ng-template>
        </nz-input-group>
      </div>

      <div class="integrations-list" *ngIf="webhooks.length > 0">
        <nz-card *ngFor="let webhook of webhooks" class="integration-item">
          <div class="integration-header">
            <div class="integration-info">
              <h4>{{ webhook.name }}</h4>
              <p class="webhook-url">{{ webhook.url }}</p>
              <div class="webhook-events">
                <nz-tag *ngFor="let event of webhook.events" nzColor="blue">
                  {{ event }}
                </nz-tag>
              </div>
            </div>
            <div class="integration-status">
              <span class="status-indicator" [class.connected]="webhook.isActive"
                [class.disconnected]="!webhook.isActive">
              </span>
              {{ webhook.isActive ? 'Active' : 'Inactive' }}
            </div>
          </div>
          <div class="integration-actions">
            <button nz-button nzType="default" nzSize="small" (click)="testWebhook(webhook)">
              <span nz-icon nzType="experiment"></span>
              Test
            </button>
            <button nz-button nzType="default" nzSize="small" nzDanger (click)="removeWebhook(webhook.id)">
              <span nz-icon nzType="delete"></span>
              Remove
            </button>
          </div>
        </nz-card>
      </div>
    </div>

    <!-- Integration Help -->
    <div class="integration-help">
      <nz-card nzTitle="Need Help?" class="help-card">
        <div class="help-content">
          <h4>How to set up integrations:</h4>
          <ol>
            <li><strong>Google Sheets:</strong> Create a new Google Sheet and copy its URL</li>
            <li><strong>Zapier:</strong> Create a new Zap with a webhook trigger and copy the webhook URL</li>
            <li><strong>Test:</strong> Use the test button to verify your integration is working</li>
          </ol>
          <div class="help-links">
            <a href="https://docs.google.com/spreadsheets" target="_blank" nz-button nzType="link">
              <span nz-icon nzType="external-link"></span>
              Create Google Sheet
            </a>
            <a href="https://zapier.com" target="_blank" nz-button nzType="link">
              <span nz-icon nzType="external-link"></span>
              Create Zapier Webhook
            </a>
          </div>
        </div>
      </nz-card>
    </div>
  </div>
</div> 