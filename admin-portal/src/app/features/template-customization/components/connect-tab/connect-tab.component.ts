import { Component, Input, Output, EventEmitter } from '@angular/core';
import { SharedModule } from '../../../../shared/shared.module';
import { Template } from 'lendsquid-templates';
import { ZapierService, ZapierWebhook, GoogleSheetsIntegration } from '../../../../core/services/zapier.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-connect-tab',
  imports: [SharedModule],
  templateUrl: './connect-tab.component.html',
  styleUrl: './connect-tab.component.less',
  standalone: true
})
export class ConnectTabComponent {
  @Input() selectedTemplate: Template | null = null;
  @Input() webhooks: ZapierWebhook[] = [];
  @Input() googleSheetsIntegrations: GoogleSheetsIntegration[] = [];
  @Output() webhookAdded = new EventEmitter<ZapierWebhook>();
  @Output() webhookRemoved = new EventEmitter<string>();
  @Output() sheetsIntegrationAdded = new EventEmitter<GoogleSheetsIntegration>();
  @Output() sheetsIntegrationRemoved = new EventEmitter<string>();

  newWebhookUrl = '';
  newSheetsUrl = '';

  constructor(
    private zapierService: ZapierService,
    private message: NzMessageService
  ) {}

  addWebhook(): void {
    if (!this.newWebhookUrl.trim()) {
      this.message.warning('Please enter a webhook URL');
      return;
    }

    if (!this.selectedTemplate?.id) {
      this.message.error('No template selected');
      return;
    }

    this.zapierService.createWebhook(this.selectedTemplate.id, {
      name: 'Lead Capture Webhook',
      url: this.newWebhookUrl,
      events: ['lead_capture', 'form_submission']
    }).subscribe({
      next: (webhook) => {
        this.webhookAdded.emit(webhook);
        this.newWebhookUrl = '';
        this.message.success('Webhook added successfully');
      },
      error: (error) => {
        console.error('Error adding webhook:', error);
        this.message.error('Failed to add webhook');
      }
    });
  }

  removeWebhook(webhookId: string): void {
    this.webhookRemoved.emit(webhookId);
  }

  testWebhook(webhook: ZapierWebhook): void {
    this.message.loading('Testing webhook...', { nzDuration: 0 });

    this.zapierService.testWebhook(webhook.id).subscribe({
      next: () => {
        this.message.remove();
        this.message.success('Webhook test successful!');
      },
      error: (error) => {
        this.message.remove();
        console.error('Error testing webhook:', error);
        this.message.error('Webhook test failed');
      }
    });
  }

  addGoogleSheets(): void {
    if (!this.newSheetsUrl.trim()) {
      this.message.warning('Please enter a Google Sheets URL');
      return;
    }

    if (!this.selectedTemplate?.id) {
      this.message.error('No template selected');
      return;
    }

    // Extract spreadsheet ID from URL
    const match = this.newSheetsUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    if (!match) {
      this.message.error('Invalid Google Sheets URL');
      return;
    }

    const spreadsheetId = match[1];

    this.zapierService.createGoogleSheetsIntegration(this.selectedTemplate.id, {
      name: 'Lead Capture Sheet',
      spreadsheetId,
      spreadsheetUrl: this.newSheetsUrl,
      worksheetName: 'Sheet1'
    }).subscribe({
      next: (integration) => {
        this.sheetsIntegrationAdded.emit(integration);
        this.newSheetsUrl = '';
        this.message.success('Google Sheets integration added successfully');
      },
      error: (error) => {
        console.error('Error adding Google Sheets integration:', error);
        this.message.error('Failed to add Google Sheets integration');
      }
    });
  }

  removeGoogleSheets(integrationId: string): void {
    this.sheetsIntegrationRemoved.emit(integrationId);
  }

  testGoogleSheets(integration: GoogleSheetsIntegration): void {
    this.message.loading('Testing Google Sheets integration...', { nzDuration: 0 });

    this.zapierService.testGoogleSheetsIntegration(integration.id).subscribe({
      next: () => {
        this.message.remove();
        this.message.success('Google Sheets integration test successful!');
      },
      error: (error) => {
        this.message.remove();
        console.error('Error testing Google Sheets integration:', error);
        this.message.error('Google Sheets integration test failed');
      }
    });
  }
} 