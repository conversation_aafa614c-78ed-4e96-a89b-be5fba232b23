.tab-content {
  padding: 24px 0;

  .connect-panel {
    h2 {
      font-size: 28px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    > p {
      font-size: 16px;
      color: #595959;
      margin-bottom: 32px;
    }
  }

  .integration-section {
    margin-bottom: 40px;

    h3 {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;

      nz-icon {
        margin-right: 8px;
        color: #1890ff;
      }
    }

    > p {
      color: #595959;
      margin-bottom: 24px;
      line-height: 1.5;
    }

    .add-integration {
      margin-bottom: 24px;

      ::ng-deep .ant-input-group-addon {
        padding: 0;
        border: none;

        button {
          border-radius: 0 6px 6px 0;
          height: 32px;
          border-left: none;
        }
      }
    }

    .integrations-list {
      .integration-item {
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .integration-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 16px;

          .integration-info {
            flex: 1;

            h4 {
              font-size: 16px;
              font-weight: 600;
              color: #262626;
              margin-bottom: 4px;
            }

            p {
              color: #595959;
              margin-bottom: 8px;
              font-size: 14px;

              &.webhook-url {
                font-family: monospace;
                background: #f5f5f5;
                padding: 4px 8px;
                border-radius: 4px;
                word-break: break-all;
              }
            }

            .sheets-link {
              display: inline-flex;
              align-items: center;
              color: #1890ff;
              font-size: 14px;
              text-decoration: none;

              nz-icon {
                margin-right: 4px;
              }

              &:hover {
                text-decoration: underline;
              }
            }

            .webhook-events {
              margin-top: 8px;

              nz-tag {
                margin-right: 4px;
                margin-bottom: 4px;
              }
            }
          }

          .integration-status {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #595959;

            .status-indicator {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-right: 8px;

              &.connected {
                background-color: #52c41a;
              }

              &.disconnected {
                background-color: #ff4d4f;
              }
            }
          }
        }

        .integration-actions {
          display: flex;
          gap: 8px;

          button {
            border-radius: 6px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .integration-help {
    .help-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      ::ng-deep .ant-card-head {
        background-color: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        border-radius: 8px 8px 0 0;

        .ant-card-head-title {
          font-weight: 600;
          color: #262626;
        }
      }

      .help-content {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 16px;
        }

        ol {
          margin-bottom: 24px;
          padding-left: 20px;

          li {
            color: #595959;
            margin-bottom: 8px;
            line-height: 1.5;

            strong {
              color: #262626;
            }
          }
        }

        .help-links {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          a {
            display: inline-flex;
            align-items: center;
            color: #1890ff;
            text-decoration: none;

            nz-icon {
              margin-right: 4px;
            }

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .tab-content {
    padding: 16px 0;

    .connect-panel {
      h2 {
        font-size: 24px;
      }
    }

    .integration-section {
      .integration-header {
        flex-direction: column;
        align-items: flex-start;

        .integration-status {
          margin-top: 12px;
        }
      }

      .integration-actions {
        flex-wrap: wrap;
      }
    }

    .integration-help {
      .help-content {
        .help-links {
          flex-direction: column;
          gap: 8px;
        }
      }
    }
  }
} 