import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Template } from 'lendsquid-templates';
import { TemplateService } from '../../core/services/template.service';
import { PaymentService, PaymentPlan } from '../../core/services/payment.service';
import { ZapierService, ZapierWebhook, GoogleSheetsIntegration } from '../../core/services/zapier.service';
import { SharedModule } from '../../shared/shared.module';
import { DomainTabComponent } from './components/domain-tab/domain-tab.component';
import { ConnectTabComponent } from './components/connect-tab/connect-tab.component';
import { CreateTabComponent } from './components/create-tab/create-tab.component';

interface TabItem {
  name: string;
  icon: string;
}

@Component({
  selector: 'app-template-customization',
  imports: [SharedModule, CreateTabComponent, DomainTabComponent, ConnectTabComponent],
  templateUrl: './template-customization.html',
  styleUrl: './template-customization.less'
})
export class TemplateCustomizationComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  selectedTemplate!: Template;
  activeTab = 0;

  // Tab configuration
  tabs: TabItem[] = [
    { name: 'Create', icon: 'edit' },
    { name: 'Domain', icon: 'global' },
    { name: 'Connect', icon: 'api' }
  ];

  // Domain tab properties
  paymentPlans: PaymentPlan[] = [];
  selectedPlan: PaymentPlan | null = null;

  // Connect tab properties
  webhooks: ZapierWebhook[] = [];
  googleSheetsIntegrations: GoogleSheetsIntegration[] = [];

  constructor(
    private templateService: TemplateService,
    private router: Router,
    private message: NzMessageService,
    private paymentService: PaymentService,
    private zapierService: ZapierService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    const selectedTemplate = this.templateService.getSelectedTemplate();

    if (!selectedTemplate) {
      // get the template id from the url
      const templateId = this.route.snapshot.paramMap.get('id');
      if (templateId) {
        this.templateService.getTemplateById(templateId).subscribe({
          next: (template) => {
            if (template) {
              this.selectedTemplate = template;
              this.cdr.detectChanges();
            }
          },
          error: (error) => {
            console.error('Error loading template:', error);
            this.message.error('No template selected. Redirecting to template selection.');
            this.router.navigate(['/templates']);
          }
        });
      }
    } else {
      this.selectedTemplate = selectedTemplate;
    }

    this.loadPaymentPlans();
    this.loadIntegrations();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onTabChange(index: number): void {
    this.activeTab = index;
  }

  goBack(): void {
    this.router.navigate(['/templates']);
  }

  // Domain Tab Methods
  loadPaymentPlans(): void {
    this.paymentService.getPaymentPlans()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (plans) => {
          this.paymentPlans = plans;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading payment plans:', error);
        }
      });
  }

  onPlanSelected(plan: PaymentPlan): void {
    this.selectedPlan = plan;
  }

  onPaymentProceeded(): void {
    // Handle payment completion
    console.log('Payment proceeded');
  }

  // Connect Tab Methods
  loadIntegrations(): void {
    if (!this.selectedTemplate?.id) return;

    // Load webhooks
    this.zapierService.getWebhooks(this.selectedTemplate.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (webhooks) => {
          this.webhooks = webhooks;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading webhooks:', error);
        }
      });

    // Load Google Sheets integrations
    this.zapierService.getGoogleSheetsIntegrations(this.selectedTemplate.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (integrations) => {
          this.googleSheetsIntegrations = integrations;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading Google Sheets integrations:', error);
        }
      });
  }

  onWebhookAdded(webhook: ZapierWebhook): void {
    this.webhooks.push(webhook);
  }

  onWebhookRemoved(webhookId: string): void {
    this.webhooks = this.webhooks.filter(w => w.id !== webhookId);
  }

  onSheetsIntegrationAdded(integration: GoogleSheetsIntegration): void {
    this.googleSheetsIntegrations.push(integration);
  }

  onSheetsIntegrationRemoved(integrationId: string): void {
    this.googleSheetsIntegrations = this.googleSheetsIntegrations.filter(i => i.id !== integrationId);
  }
}
