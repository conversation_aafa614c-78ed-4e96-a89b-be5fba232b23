<div id="template-customization-root" class="template-customization-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="container">
      <div class="header-content">
        <div class="header-left">
          <button nz-button nzType="text" nzSize="large" class="back-button" (click)="goBack()">
            <span nz-icon nzType="arrow-left"></span>
            Back to Templates
          </button>
        </div>
        <div class="header-tabs-actions">
          <div class="header-tabs">
            <span *ngFor="let tab of tabs; let i = index" class="header-tab-label"
              [ngClass]="{ 'active': activeTab === i }" (click)="onTabChange(i)">
              <nz-icon [nzType]="tab.icon" class="tab-icon"></nz-icon>
              {{ tab.name }}
            </span>
          </div>
        </div>
        <div class="header-actions">
          <button nz-button nzType="default" nzShape="round" class="save-btn" data-testid="save-btn">
            <span nz-icon nzType="save"></span>
            Save Progress
          </button>
          <button nz-button nzType="primary" nzShape="round" class="publish-btn" data-testid="publish-btn">
            <span nz-icon nzType="cloud-upload"></span>
            Publish Website
          </button>
        </div>
      </div>
      <div class="template-info" *ngIf="selectedTemplate">
        <h1 class="page-title">Customize {{ selectedTemplate.name }}</h1>
        <p class="page-description">{{ selectedTemplate.description }}</p>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <!-- Create Tab Content -->
    <app-create-tab *ngIf="activeTab === 0" [selectedTemplate]="selectedTemplate">
    </app-create-tab>

    <!-- Domain Tab Content -->
    <app-domain-tab *ngIf="activeTab === 1" [paymentPlans]="paymentPlans" [selectedPlan]="selectedPlan"
      (planSelected)="onPlanSelected($event)" (paymentProceeded)="onPaymentProceeded()">
    </app-domain-tab>

    <!-- Connect Tab Content -->
    <app-connect-tab *ngIf="activeTab === 2" [selectedTemplate]="selectedTemplate" [webhooks]="webhooks"
      [googleSheetsIntegrations]="googleSheetsIntegrations" (webhookAdded)="onWebhookAdded($event)"
      (webhookRemoved)="onWebhookRemoved($event)" (sheetsIntegrationAdded)="onSheetsIntegrationAdded($event)"
      (sheetsIntegrationRemoved)="onSheetsIntegrationRemoved($event)">
    </app-connect-tab>
  </div>
</div>