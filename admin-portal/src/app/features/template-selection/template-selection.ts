import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil, finalize } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TemplateService } from '../../core/services/template.service';
import { Template } from 'lendsquid-templates';
import { SharedModule } from '../../shared/shared.module';

@Component({
  selector: 'app-template-selection',
  imports: [SharedModule],
  templateUrl: './template-selection.html',
  styleUrls: ['./template-selection.less']
})
export class TemplateSelectionComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private loadingState = signal<boolean>(false);
  templates = signal<Template[]>([]);
  selectedTemplate = signal<Template | null>(null);
  loading = computed(() => this.loadingState());

  constructor(
    private templateService: TemplateService,
    private router: Router,
    private message: NzMessageService,
  ) {}

  ngOnInit(): void {
    this.loadTemplates();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadTemplates(): void {
    this.loadingState.set(true);
    
    this.templateService.getTemplates()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.loadingState.set(false);
        })
      )
      .subscribe({
        next: (templates) => {
          // Add coming soon templates
          const comingSoonTemplates: Template[] = [
            {
              id: 'coming-soon-1',
              name: 'Modern Finance',
              description: 'A sleek, modern template designed for financial services with advanced calculators and professional styling.',
              thumbnail: 'assets/images/coming-soon-1.svg',
              theme: {
                primaryColor: '#6366f1',
                secondaryColor: '#8b5cf6',
                backgroundColor: '#ffffff',
                secondaryBackgroundColor: '#ffffff',
                primaryTextColor: '#1f2937',
                secondaryTextColor: '#1f2937',
                tertiaryTextColor: '#1f2937',
                fontFamily: 'Inter, sans-serif',
              },
              sections: [],
              metadata: {
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                version: '1.0.0',
                author: 'LendSquid Team'
              }
            },
            {
              id: 'coming-soon-2',
              name: 'Real Estate Pro',
              description: 'Specialized template for real estate professionals with property valuation tools and lead capture forms.',
              thumbnail: 'assets/images/coming-soon-2.svg',
              theme: {
                primaryColor: '#059669',
                secondaryColor: '#10b981',
                backgroundColor: '#ffffff',
                secondaryBackgroundColor: '#ffffff',
                primaryTextColor: '#1f2937',
                secondaryTextColor: '#1f2937',
                tertiaryTextColor: '#1f2937',
                fontFamily: 'Inter, sans-serif',
              },
              sections: [],
              metadata: {
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                version: '1.0.0',
                author: 'LendSquid Team'
              }
            },
            {
              id: 'coming-soon-3',
              name: 'Luxury Lending',
              description: 'Premium template for high-end lending services with sophisticated design and advanced features.',
              thumbnail: 'assets/images/coming-soon-3.svg',
              theme: {
                primaryColor: '#1e293b',
                secondaryColor: '#475569',
                backgroundColor: '#ffffff',
                secondaryBackgroundColor: '#ffffff',
                primaryTextColor: '#1f2937',
                secondaryTextColor: '#1f2937',
                tertiaryTextColor: '#1f2937',
                fontFamily: 'Inter, sans-serif',
              },
              sections: [],
              metadata: {
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                version: '1.0.0',
                author: 'LendSquid Team'
              }
            }
          ];

          // Combine active templates with coming soon templates
          const allTemplates = [...templates, ...comingSoonTemplates];
          this.templates.set(allTemplates);
        },
        error: (error) => {
          console.error('Error loading templates:', error);
          this.message.error('Failed to load templates');
        }
      });
  }

  selectTemplate(template: Template): void {
    // Only allow selection of active templates
    if (template.id.startsWith('template-')) {
      this.selectedTemplate.set(template);
      this.templateService.setSelectedTemplate(template);
    }
  }

  previewTemplate(template: Template): void {
    // Only allow preview of active templates
    if (template.id.startsWith('template-')) {
      const previewUrl = `/templates/preview/${template.id}`;
      window.open(previewUrl, '_blank');
    }
  }

  customizeWithAI(): void {
    // Navigate to customization page with template ID
    this.router.navigate(['/templates/customize', this.selectedTemplate()?.id]);
  }

  goBack(): void {
    this.router.navigate(['/']);
  }
}
