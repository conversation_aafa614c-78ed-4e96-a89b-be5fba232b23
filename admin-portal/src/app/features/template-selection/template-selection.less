.template-selection-container {
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  // Header Section
  .header-section {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 24px 0;

    .back-button {
      margin-bottom: 16px;
      color: #666;

      &:hover {
        color: #1890ff;
      }
    }

    .header-content {
      text-align: center;

      .page-title {
        font-size: 32px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 8px;
      }

      .page-description {
        font-size: 16px;
        color: #8c8c8c;
        margin-bottom: 0;
      }
    }
  }

  // Templates Section
  .templates-section {
    padding: 48px 0;
    flex: 1;
    overflow-y: auto;

    .empty-state {
      text-align: center;
      padding: 48px 0;
    }

    .templates-grid {
      .template-col {
        .template-card {
          height: 100%;
          border-radius: 12px;
          overflow: hidden;
          transition: all 0.3s ease;
          border: 2px solid transparent;
          background: white;

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
          }

          // Active Template Styling
          &.active-template {
            border-color: transparent;

            &:hover {
              border-color: #1890ff;
            }

            &.selected {
              border-color: #1890ff;
              box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
            }

            .template-cover {
              .template-overlay {
                opacity: 0;
                transition: opacity 0.3s ease;
              }

              &:hover {
                .template-overlay {
                  opacity: 1;
                }
              }
            }

            .template-info {
              .template-name {
                color: #262626;
              }

              .template-description {
                color: #8c8c8c;
              }
            }

            .template-actions {
              .cta-button {
                &:not(:disabled) {
                  &:hover {
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }

          // Coming Soon Template Styling
          &.coming-soon-card {
            opacity: 0.85;
            filter: grayscale(20%);
            border-color: #f0f0f0;
            background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);

            &:hover {
              opacity: 1;
              filter: grayscale(0%);
              border-color: #722ed1;
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(114, 46, 209, 0.15);
            }

            .template-cover {
              .coming-soon-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(114, 46, 209, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;

                .coming-soon-badge {
                  background: white;
                  color: #722ed1;
                  padding: 8px 16px;
                  border-radius: 20px;
                  font-weight: 600;
                  font-size: 14px;
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

                  .anticon {
                    font-size: 16px;
                  }
                }
              }
            }

            .template-info {
              .template-name {
                color: #722ed1;
                font-weight: 700;
              }

              .template-description {
                color: #666;
                font-style: italic;
              }

              .premium-tag {
                background: linear-gradient(135deg, #722ed1, #9254de);
                border: none;
                color: white;
              }
            }

            .template-actions {
              .cta-button {
                &:disabled {
                  background: #f0f0f0;
                  border-color: #d9d9d9;
                  color: #bfbfbf;
                  cursor: not-allowed;
                }
              }
            }
          }

          .template-cover {
            position: relative;
            height: 200px;
            overflow: hidden;

            .template-thumbnail {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }

            .template-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.5);
              display: flex;
              align-items: center;
              justify-content: center;

              .preview-button {
                background: white;
                border: none;
                color: #1890ff;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 500;

                &:hover {
                  background: #f0f0f0;
                }
              }
            }

            &:hover {
              .template-thumbnail {
                transform: scale(1.05);
              }
            }
          }

          .template-info {
            padding: 16px 0;

            .template-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 8px;

              .template-name {
                font-weight: 600;
                margin: 0;
                font-size: 16px;
              }

              .premium-tag {
                font-size: 12px;
                margin: 0;

                .anticon {
                  margin-right: 4px;
                }
              }
            }

            .template-description {
              font-size: 14px;
              line-height: 1.5;
              margin: 0;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }

          .template-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
  }

  // Action Section
  .action-section {
    background: white;
    border-top: 1px solid #f0f0f0;
    padding: 24px 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

    .selected-template-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 24px;

      .selected-template-details {
        flex: 1;

        h3 {
          margin-bottom: 4px;
          color: #262626;
          font-weight: 600;
        }

        p {
          margin-bottom: 0;
          color: #8c8c8c;
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
      }
    }
  }

  .cta-button {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;

    &[nzType="primary"] {
      background: #6366f1;
      border: none;
      color: white;

      &:hover {
        background: #4f46e5;
      }

      &:disabled {
        background: #d1d5db;
        color: #9ca3af;
        cursor: not-allowed;
      }
    }

    &[nzType="default"] {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      color: #4b5563;

      &:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
      }

      &:disabled {
        background: #f3f4f6;
        border-color: #e5e7eb;
        color: #9ca3af;
        cursor: not-allowed;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .template-selection-container {
    .header-section {
      .header-content {
        .page-title {
          font-size: 24px;
        }
      }
    }

    .templates-section {
      .template-section {
        .section-header {
          .section-title {
            font-size: 24px;
          }
        }
      }
    }

    .action-section {
      .selected-template-info {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;

        .action-buttons {
          justify-content: center;
        }
      }
    }
  }
}