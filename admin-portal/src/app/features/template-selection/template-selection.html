<div class="template-selection-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="container">
      <button nz-button nzType="text" nzSize="large" class="back-button" (click)="goBack()">
        <span nz-icon nzType="arrow-left"></span>
        Back
      </button>
      <div class="header-content">
        <h1 class="page-title">Choose Your Template</h1>
        <p class="page-description">
          Select a template that best matches your business style. You can customize it later.
        </p>
      </div>
    </div>
  </div>

  <!-- Templates Grid Section -->
  <div class="templates-section">
    <div class="container">
      <nz-spin [nzSpinning]="loading()" nzTip="Loading templates...">
        <div *ngIf="!loading() && templates().length === 0" class="empty-state">
          <nz-empty nzNotFoundImage="simple" nzNotFoundContent="No templates available">
          </nz-empty>
        </div>

        <!-- All Templates Section -->
        <div *ngIf="templates().length > 0" class="template-section">
          
          <div nz-row [nzGutter]="[24, 24]" class="templates-grid">
            <div
              *ngFor="let template of templates()"
              nz-col
              [nzXs]="24"
              [nzSm]="12"
              [nzMd]="8"
              [nzLg]="6"
              class="template-col">
              
              <!-- Active Template Card -->
              <nz-card 
                *ngIf="template.id.startsWith('template-'); else comingSoonTemplate"
                class="template-card active-template" 
                [class.selected]="selectedTemplate()?.id === template.id" 
                nzHoverable
                [nzCover]="activeTemplateCover">

                <!-- Active Template Cover Image -->
                <ng-template #activeTemplateCover>
                  <div class="template-cover">
                    <img [src]="template.thumbnail" [alt]="template.name" class="template-thumbnail">
                  </div>
                </ng-template>

                <!-- Template Actions -->
                <div class="template-actions">
                  <button nz-button nzType="primary" class="cta-button use-template-button"
                    (click)="selectTemplate(template)">
                    Use Template
                  </button>
                  <button nz-button nzType="default" class="cta-button preview-button"
                    (click)="previewTemplate(template)">
                    Preview
                    <nz-icon nzType="export" nzTheme="outline" />
                  </button>
                </div>
              </nz-card>

              <!-- Coming Soon Template Card -->
              <ng-template #comingSoonTemplate>
                <nz-card
                class="template-card coming-soon-card" 
                nzHoverable
                [nzCover]="comingSoonCover">

                <!-- Coming Soon Cover Image -->
                <ng-template #comingSoonCover>
                  <div class="template-cover coming-soon-cover">
                    <img [src]="template.thumbnail" [alt]="template.name" class="template-thumbnail"
                      >
                    <div class="coming-soon-overlay">
                      <div class="coming-soon-badge">
                        <nz-icon nzType="clock-circle" nzTheme="outline"></nz-icon>
                        Coming Soon
                      </div>
                    </div>
                  </div>
                </ng-template>

                <!-- Disabled Template Actions -->
                <div class="template-actions">
                  <button nz-button nzType="primary" class="cta-button use-template-button" [disabled]="true" nz-tooltip
                    nzTooltipTitle="This template will be available soon!">
                    Coming Soon
                  </button>
                  <button nz-button nzType="default" class="cta-button preview-button" [disabled]="true" nz-tooltip
                    nzTooltipTitle="Preview will be available when template launches">
                    Preview
                    <nz-icon nzType="export" nzTheme="outline" />
                  </button>
                </div>
                </nz-card>
              </ng-template>
            </div>
          </div>
        </div>
      </nz-spin>
    </div>
  </div>

  <!-- Action Section -->
  <div class="action-section" *ngIf="selectedTemplate()">
    <div class="container">
      <div class="selected-template-info">
        <div class="selected-template-details">
          <h3>Selected Template: {{ selectedTemplate()?.name }}</h3>
          <p>{{ selectedTemplate()?.description }}</p>
        </div>
        <div class="action-buttons">
          <button nz-button nzType="primary" nzSize="large" class="cta-button continue-with-template-button"
            (click)="customizeWithAI()">
            Continue with Template
            <span nz-icon nzType="arrow-right"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>