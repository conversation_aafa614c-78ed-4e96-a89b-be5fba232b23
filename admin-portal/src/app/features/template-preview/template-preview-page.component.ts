import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { TemplateService } from '../../core/services/template.service';
import { TemplatePreviewComponent } from 'lendsquid-templates';
import { Template } from 'lendsquid-templates';
import { SharedModule } from '../../shared/shared.module';

@Component({
  selector: 'app-template-preview-page',
  imports: [SharedModule, TemplatePreviewComponent],
  templateUrl: './template-preview-page.component.html',
  styleUrls: ['./template-preview-page.component.less']
})
export class TemplatePreviewPageComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  template: Template | null = null;

  constructor(
    private route: ActivatedRoute,
    private templateService: TemplateService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    // If no template in preview service, load from route params
    this.route.params
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.loadTemplate(params['id']);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadTemplate(templateId: string): void {
    if (!templateId) return;

    this.templateService.getTemplateById(templateId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (template) => {
          this.template = template;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading template:', error);
        }
      });
  }
} 