// Less variables for colors, fonts, etc.

// Light theme colors palette
:root {
    // Gray Neutral
    --neutral-1: #ffffff;
    --neutral-2: #fafafa;
    --neutral-3: #f5f5f5;
    --neutral-4: #f0f0f0;
    --neutral-5: #d9d9d9;
    --neutral-6: #bfbfbf;
    --neutral-7: #8c8c8c;
    --neutral-8: #595959;
    --neutral-9: #434343;
    --neutral-10: #262626;
    --neutral-11: #1f1f1f;
    --neutral-12: #141414;
    --neutral-13: #000000;

    /* Ant Design Colors */
    --blue-1: #e6f7ff;
    --blue-2: #bae7ff;
    --blue-3: #91d5ff;
    --blue-4: #69c0ff;
    --blue-5: #40a9ff;
    --blue-6: #1890ff;
    --blue-7: #096dd9;
    --blue-8: #0050b3;
    --blue-9: #003a8c;
    --blue-10: #002766;

    --purple-1: #f9f0ff;
    --purple-2: #efdbff;
    --purple-3: #d3adf7;
    --purple-4: #b37feb;
    --purple-5: #9254de;
    --purple-6: #722ed1;
    --purple-7: #531dab;
    --purple-8: #391085;
    --purple-9: #22075e;
    --purple-10: #120338;

    --cyan-1: #e6fffb;
    --cyan-2: #b5f5ec;
    --cyan-3: #87e8de;
    --cyan-4: #5cdbd3;
    --cyan-5: #36cfc9;
    --cyan-6: #13c2c2;
    --cyan-7: #08979c;
    --cyan-8: #006d75;
    --cyan-9: #00474f;
    --cyan-10: #002329;

    --green-1: #f6ffed;
    --green-2: #d9f7be;
    --green-3: #b7eb8f;
    --green-4: #95de64;
    --green-5: #73d13d;
    --green-6: #52c41a;
    --green-7: #389e0d;
    --green-8: #237804;
    --green-9: #135200;
    --green-10: #092b00;

    --magenta-1: #fff0f6;
    --magenta-2: #ffd6e7;
    --magenta-3: #ffadd2;
    --magenta-4: #ff85c0;
    --magenta-5: #f759ab;
    --magenta-6: #eb2f96;
    --magenta-7: #c41d7f;
    --magenta-8: #9e1068;
    --magenta-9: #780650;
    --magenta-10: #520339;

    --pink-1: #fff0f6;
    --pink-2: #ffd6e7;
    --pink-3: #ffadd2;
    --pink-4: #ff85c0;
    --pink-5: #f759ab;
    --pink-6: #eb2f96;
    --pink-7: #c41d7f;
    --pink-8: #9e1068;
    --pink-9: #780650;
    --pink-10: #520339;

    --red-1: #fff1f0;
    --red-2: #ffccc7;
    --red-3: #ffa39e;
    --red-4: #ff7875;
    --red-5: #ff4d4f;
    --red-6: #f5222d;
    --red-7: #cf1322;
    --red-8: #a8071a;
    --red-9: #820014;
    --red-10: #5c0011;

    --orange-1: #fff7e6;
    --orange-2: #ffe7ba;
    --orange-3: #ffd591;
    --orange-4: #ffc069;
    --orange-5: #ffa940;
    --orange-6: #fa8c16;
    --orange-7: #d46b08;
    --orange-8: #ad4e00;
    --orange-9: #873800;
    --orange-10: #612500;

    --yellow-1: #feffe6;
    --yellow-2: #ffffb8;
    --yellow-3: #fffb8f;
    --yellow-4: #fff566;
    --yellow-5: #ffec3d;
    --yellow-6: #fadb14;
    --yellow-7: #d4b106;
    --yellow-8: #ad8b00;
    --yellow-9: #876800;
    --yellow-10: #614700;

    --volcano-1: #fff2e8;
    --volcano-2: #ffd8bf;
    --volcano-3: #ffbb96;
    --volcano-4: #ff9c6e;
    --volcano-5: #ff7a45;
    --volcano-6: #fa541c;
    --volcano-7: #d4380d;
    --volcano-8: #ad2102;
    --volcano-9: #871400;
    --volcano-10: #610b00;

    --geekblue-1: #f0f5ff;
    --geekblue-2: #d6e4ff;
    --geekblue-3: #adc6ff;
    --geekblue-4: #85a5ff;
    --geekblue-5: #597ef7;
    --geekblue-6: #2f54eb;
    --geekblue-7: #1d39c4;
    --geekblue-8: #10239e;
    --geekblue-9: #061178;
    --geekblue-10: #030852;

    --lime-1: #fcffe6;
    --lime-2: #f4ffb8;
    --lime-3: #eaff8f;
    --lime-4: #d3f261;
    --lime-5: #bae637;
    --lime-6: #a0d911;
    --lime-7: #7cb305;
    --lime-8: #5b8c00;
    --lime-9: #3f6600;
    --lime-10: #254000;
}

// Dark theme colors palette
body.dark-theme {

    // Gray Neutral
    --neutral-1: #1f1f21;
    --neutral-2: #242528;
    --neutral-3: #2b2c2f;
    --neutral-4: #303134;
    --neutral-5: #3d3f43;
    --neutral-6: #4b4d51;
    --neutral-7: #63666b;
    --neutral-8: #7e8188;
    --neutral-9: #96999e;
    --neutral-10: #a9abaf;
    --neutral-11: #bfc1c4;
    --neutral-12: #cecfd2;
    --neutral-13: #e2e3e4;

    // Red
    --red-1: #2a1215;
    --red-2: #431418;
    --red-3: #58181c;
    --red-4: #791a1f;
    --red-5: #a61d24;
    --red-6: #d32029;
    --red-7: #e84749;
    --red-8: #f37370;
    --red-9: #f89a9a;
    --red-10: #fac8c3;

    // Volcano
    --volcano-1: #2b1611;
    --volcano-2: #441d12;
    --volcano-3: #592716;
    --volcano-4: #7c3118;
    --volcano-5: #aa3e19;
    --volcano-6: #d84a1b;
    --volcano-7: #e87040;
    --volcano-8: #f3956a;
    --volcano-9: #f8b692;
    --volcano-10: #fad4bc;

    // Orange
    --orange-1: #2b1d11;
    --orange-2: #442a11;
    --orange-3: #593815;
    --orange-4: #7c4a15;
    --orange-5: #aa6215;
    --orange-6: #d87a16;
    --orange-7: #e89a3c;
    --orange-8: #f3b765;
    --orange-9: #f8cf8d;
    --orange-10: #fae3b7;

    // Gold
    --gold-1: #2b2111;
    --gold-2: #443111;
    --gold-3: #594214;
    --gold-4: #7c5914;
    --gold-5: #aa7714;
    --gold-6: #d89614;
    --gold-7: #e8b339;
    --gold-8: #f3cc62;
    --gold-9: #f8df8b;
    --gold-10: #faedb5;

    // Yellow
    --yellow-1: #2b2611;
    --yellow-2: #443611;
    --yellow-3: #595014;
    --yellow-4: #7c6814;
    --yellow-5: #aa9514;
    --yellow-6: #d8bd14;
    --yellow-7: #e8d639;
    --yellow-8: #f3ea62;
    --yellow-9: #f8f48b;
    --yellow-10: #fafab5;

    // Lime
    --lime-1: #112611;
    --lime-2: #2e3c10;
    --lime-3: #3e4f13;
    --lime-4: #536d13;
    --lime-5: #6f9412;
    --lime-6: #8bbb11;
    --lime-7: #a9d134;
    --lime-8: #c9e75d;
    --lime-9: #e4f88b;
    --lime-10: #f0fab5;

    // Green
    --green-1: #162312;
    --green-2: #163712;
    --green-3: #274916;
    --green-4: #306317;
    --green-5: #3c8618;
    --green-6: #49aa19;
    --green-7: #6abe39;
    --green-8: #8fd460;
    --green-9: #b2e58b;
    --green-10: #d5f2bb;

    // Cyan
    --cyan-1: #112123;
    --cyan-2: #113536;
    --cyan-3: #144848;
    --cyan-4: #146262;
    --cyan-5: #138585;
    --cyan-6: #13a8a8;
    --cyan-7: #33bcb7;
    --cyan-8: #58d1c9;
    --cyan-9: #84e2d8;
    --cyan-10: #b2f1e8;

    // Blue
    --blue-1: #1c2b42;
    --blue-2: #112a45;
    --blue-3: #15395b;
    --blue-4: #164c7e;
    --blue-5: #1765ad;
    --blue-6: #177ddc;
    --blue-7: #3c9ae8;
    --blue-8: #65b7f3;
    --blue-9: #8dcff8;
    --blue-10: #b7e3fa;

    // Geekblue
    --geekblue-1: #131629;
    --geekblue-2: #161d40;
    --geekblue-3: #1c2755;
    --geekblue-4: #203175;
    --geekblue-5: #263ea0;
    --geekblue-6: #2b4acb;
    --geekblue-7: #5273e0;
    --geekblue-8: #719ef3;
    --geekblue-9: #a8c1f8;
    --geekblue-10: #d2e0fa;

    // Purple
    --purple-1: #1a1325;
    --purple-2: #24163a;
    --purple-3: #301c4d;
    --purple-4: #3e2069;
    --purple-5: #51258f;
    --purple-6: #642ab5;
    --purple-7: #854eca;
    --purple-8: #ab7ae0;
    --purple-9: #cda8f0;
    --purple-10: #ebd7fa;

    // Magenta
    --magenta-1: #291321;
    --magenta-2: #40162f;
    --magenta-3: #551c3b;
    --magenta-4: #75204f;
    --magenta-5: #a02669;
    --magenta-6: #cb2b83;
    --magenta-7: #e0529c;
    --magenta-8: #f37fb7;
    --magenta-9: #f8a8cc;
    --magenta-10: #fad2e3;
}

// Border Radius
@border-radius-base: 6px;
@border-radius-sm: 4px;
@border-radius-lg: 8px;

// Border Colors
@border-color: var(--neutral-5);
@border-color-light: var(--neutral-4);
@border-color-dark: var(--neutral-6);

// Shadow
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
@box-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.12);
@box-shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.15);

// Font Sizes
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;
@font-size-xl: 18px;
@font-size-xxl: 20px;

// Font Weights
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// Line Heights
@line-height-base: 1.5715;
@line-height-lg: 1.5;
@line-height-sm: 1.66;

// Spacing
@padding-xs: 8px;
@padding-sm: 12px;
@padding-md: 16px;
@padding-lg: 24px;
@padding-xl: 32px;

@margin-xs: 8px;
@margin-sm: 12px;
@margin-md: 16px;
@margin-lg: 24px;
@margin-xl: 32px;

// Breakpoints
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// Z-index
@zindex-dropdown: 1050;
@zindex-sticky: 1020;
@zindex-fixed: 1030;
@zindex-modal-backdrop: 1040;
@zindex-modal: 1050;
@zindex-popover: 1060;
@zindex-tooltip: 1070;

// Animation
@animation-duration-slow: 0.3s;
@animation-duration-base: 0.2s;
@animation-duration-fast: 0.1s;

// Colors
@primary-color: var(--primary-color);
@success-color: var(--green-6);
@warning-color: var(--orange-6);
@error-color: var(--red-5);
@info-color: var(--blue-6);

// Gradient Colors
@gradient-primary: linear-gradient(135deg, @primary-color, var(--purple-6));
@gradient-success: linear-gradient(135deg, @success-color, var(--green-6));
@gradient-warning: linear-gradient(135deg, @warning-color, var(--orange-6));
@gradient-error: linear-gradient(135deg, @error-color, var(--red-6));

// Application Colors
@primary-text: var(--neutral-13);
@secondary-text: var(--neutral-8);
@primary-color: var(--blue-6);
@text-selected: var(--blue-6);
@primary-color-bg: var(--blue-1);
@secondary-color: var(--blue-6);
@section-bg-light: var(--neutral-2);
@section-bg-white: var(--neutral-1);

// Application Variables
@header-height: 54px;
@sub-header-height: calc(@header-height + 10px);
@main-content-margin: 0px;
@main-content-el-padding: 1rem;
@main-el-gap: 1rem;